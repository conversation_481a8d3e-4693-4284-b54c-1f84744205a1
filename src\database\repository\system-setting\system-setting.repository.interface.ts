import { SystemSetting } from '@database/schemas/system-setting.model';
import { BaseInterfaceRepository } from '@core/repository/base.interface.repository';

export interface SystemSettingRepositoryInterface
  extends BaseInterfaceRepository<SystemSetting> {
  createEntity(data: {
    key: string;
    value: any;
    description?: string;
    createdBy?: string;
  }): SystemSetting;

  updateEntity(
    entity: SystemSetting,
    data: {
      value: any;
      description?: string;
      updatedBy?: string;
    },
  ): SystemSetting;

  findByKey(key: string): Promise<SystemSetting | null>;

  getValueByKey(key: string): Promise<any | null>;

  setValueByKey(
    key: string,
    value: any,
    description?: string,
    updatedBy?: string,
  ): Promise<SystemSetting>;
}
