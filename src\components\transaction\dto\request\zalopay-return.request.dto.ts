import { Expose } from 'class-transformer';

import { BaseDto } from '@core/dto/base.request.dto';

export class ZaloPayReturnRequestDto extends BaseDto {
  @Expose()
  amount: number;

  @Expose({ name: 'appid' })
  appId: string;

  @Expose({ name: 'apptransid' })
  appTransId: string;

  @Expose({ name: 'bankcode' })
  bankCode?: string;

  @Expose({ name: 'checksum' })
  checksum: string;

  @Expose({ name: 'discountamount' })
  discountAmount: number;

  @Expose({ name: 'pmcid' })
  pmcId: number;

  @Expose({ name: 'status' })
  status: number;
}
