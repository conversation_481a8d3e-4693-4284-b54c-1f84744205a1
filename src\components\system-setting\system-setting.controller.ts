import { isEmpty } from 'lodash';
import { Body, Controller, Get, Param, Put } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { UseGuards } from '@nestjs/common';
import { RoleGuard } from '@core/guards/role.guard';
import { Roles } from '@core/decorators/roles.decorator';
import { Public } from '@core/decorators/public.decorator';
import { IdParamDto } from '@core/dto/param-id.request.dto';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { SystemSettingService } from './system-setting.service';
import { UpdateSystemSettingRequestDto } from './dto/request/update-system-setting.request.dto';

@ApiBearerAuth()
@Controller('system-settings')
export class SystemSettingController {
  constructor(private readonly systemSettingService: SystemSettingService) {}

  @Public()
  @Get('/cloudflare-captcha')
  @ApiOperation({
    tags: ['System Settings'],
    summary: 'L<PERSON>y trạng thái Cloudflare Captcha',
    description: 'Lấy trạng thái Cloudflare Captcha',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async getCloudflareCaptchaStatus() {
    return await this.systemSettingService.getCloudflareCaptchaStatus();
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN, USER_ROLE_ENUM.VIEWER)
  @Get('/')
  @ApiOperation({
    tags: ['System Settings'],
    summary: 'Lấy danh sách cài đặt hệ thống',
    description: 'Lấy danh sách cài đặt hệ thống',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async getAllSettings() {
    return await this.systemSettingService.getAllSettings();
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN, USER_ROLE_ENUM.VIEWER)
  @Put('/:id')
  @ApiOperation({
    tags: ['System Settings'],
    summary: 'Cập nhật cài đặt hệ thống',
    description: 'Cập nhật cài đặt hệ thống',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async updateSystemSetting(
    @Param() param: IdParamDto,
    @Body() payload: UpdateSystemSettingRequestDto,
  ) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.systemSettingService.updateSystemSetting({
      ...request,
      id: param?.request?.id,
    });
  }
}
