import { I18nService } from 'nestjs-i18n';
import { OnEvent } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import { Inject, Injectable } from '@nestjs/common';

import { EVENT_ENUM } from '@constant/event.enum';
import { BaseDto } from '@core/dto/base.request.dto';
import { ResponseBuilder } from '@utils/response-builder';
import { PaginationQuery } from '@core/dto/pagination.query';
import { BaseResponseDto } from '@core/dto/base.response.dto';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ListChatRequestDto } from './dto/request/list-chat.request.dto';
import { CreateChatRequestDto } from './dto/request/create-chat.request.dto';
import { MessageDetailResponseDto } from './dto/response/message-detail.response.dto';
import { ChatRepositoryInterface } from '@database/repository/chat/chat.repository.interface';
import { ListLastChatForAdminResponseDto } from './dto/response/list-last-chat-for-admin.response.dto';

@Injectable()
export class ChatService {
  constructor(
    private readonly i18n: I18nService,

    @Inject('ChatRepositoryInterface')
    private readonly chatRepository: ChatRepositoryInterface,
  ) {}

  @OnEvent(EVENT_ENUM.SAVE_MESSAGE)
  async create(request: CreateChatRequestDto) {
    const chatEntity = this.chatRepository.createEntity(request);
    await chatEntity.save();

    const response = plainToInstance(BaseResponseDto, chatEntity, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.CREATED)
      .withMessage(this.i18n.translate('message.CREATE_SUCCESS'))
      .build();
  }

  async listForUser(request: ListChatRequestDto) {
    const { data, total } = await this.chatRepository.listForUser(request);

    const response = plainToInstance(MessageDetailResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      meta: { total, page: request.page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async listForAdmin(request: ListChatRequestDto, userId: string) {
    const { data, total } = await this.chatRepository.listForAdmin(
      request,
      userId,
    );

    const response = plainToInstance(MessageDetailResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      meta: { total, page: request.page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async listLastChatForAdmin(request: PaginationQuery) {
    const { data, total } =
      await this.chatRepository.listLastChatForAdmin(request);

    const response = plainToInstance(ListLastChatForAdminResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      meta: { total, page: request.page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async reset(request: BaseDto): Promise<any> {
    const { userId } = request;

    await this.chatRepository.deleteMany({
      $or: [{ sender: userId }, { receiver: userId }],
    });

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }
}
