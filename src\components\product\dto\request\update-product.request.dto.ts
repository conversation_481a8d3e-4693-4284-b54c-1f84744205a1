import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';

import { CreateProductRequestDto } from './create-product.request.dto';
import { PRODUCT_STATUS_ENUM } from '@components/product/product.constant';

export class UpdateProductRequestDto extends CreateProductRequestDto {
  @IsOptional()
  @IsString()
  id: string;

  @IsEnum(PRODUCT_STATUS_ENUM)
  @IsNotEmpty()
  status: PRODUCT_STATUS_ENUM;
}
