import { isEmpty } from 'lodash';
import { Model, Types } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { generateRandomString } from '@helpers/string.helper';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { Transaction } from '@database/schemas/transaction.model';
import { convertOrderMongo, getRegexByValue, SortOrder } from '@utils/common';
import { BaseAbstractRepository } from '@core/repository/base.abstract.repository';
import { TransactionRepositoryInterface } from './transaction.repository.interface';
import { CreateTransactionPayload } from '@components/transaction/dto/request/create-transaction.payload';
import { GetListTransactionRequestDto } from '@components/transaction/dto/request/get-list-transaction.request.dto';

@Injectable()
export class TransactionRepository
  extends BaseAbstractRepository<Transaction>
  implements TransactionRepositoryInterface
{
  constructor(
    @InjectModel(Transaction.name)
    private readonly transactionModel: Model<Transaction>,
  ) {
    super(transactionModel);
  }

  createEntity(payload: CreateTransactionPayload): Transaction {
    const entity = new this.transactionModel();

    entity.user = payload.user;
    entity.amount = payload.amount;
    entity.code = generateRandomString(6);
    entity.balanceBefore = payload.balanceBefore;
    entity.balanceAfter = payload.balanceAfter;
    entity.action = payload.action;
    entity.note = payload.note;

    return entity;
  }

  async list(
    request: GetListTransactionRequestDto,
    role?: USER_ROLE_ENUM,
  ): Promise<{ data: Transaction[]; total: number }> {
    const { userId, page, limit, keyword, sort } = request;

    const take = limit;
    const skip = (page - 1) * limit;

    let filterObj: any = {};
    let populateObj: any = {};
    let sortObj: any = { createdAt: SortOrder.DESC };

    if (!isEmpty(keyword)) {
      const filterByKeyword = getRegexByValue(keyword);
      filterObj = {
        $or: [{ code: filterByKeyword }, { note: filterByKeyword }],
      };
    }

    if (role === USER_ROLE_ENUM.USER) {
      filterObj = {
        ...filterObj,
        user: new Types.ObjectId(userId),
      };
    }

    if (!isEmpty(sort)) {
      sort.forEach((item) => {
        const order = convertOrderMongo(item.order);
        switch (item.column) {
          case 'createdAt':
            sortObj = { ...sortObj, createdAt: order };
            break;
          default:
            break;
        }
      });
    }

    if (role === USER_ROLE_ENUM.ADMIN) {
      populateObj = {
        path: 'user',
        select: 'email fullname',
      };
    }

    const [transactions, total] = await Promise.all([
      this.transactionModel
        .find(filterObj)
        .populate(isEmpty(populateObj) ? undefined : populateObj)
        .limit(take)
        .skip(skip)
        .sort(sortObj)
        .exec(),
      this.transactionModel.countDocuments(filterObj).exec(),
    ]);

    return { data: transactions, total };
  }
}
