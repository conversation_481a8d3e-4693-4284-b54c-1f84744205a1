import * as crypto from 'crypto';
import * as moment from 'moment';
import * as queryString from 'qs';
import { I18nService } from 'nestjs-i18n';
import { Connection, Types } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import { InjectConnection } from '@nestjs/mongoose';
import { intersection, isEmpty, map } from 'lodash';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Inject, Injectable, Logger } from '@nestjs/common';

import {
  ORDER_CONST,
  ORDER_STATUS_ENUM,
  PAYMENT_METHOD_ENUM,
  PAYMENT_STATUS_ENUM,
  ORDER_HISTORY_ACTION_ENUM,
} from './order.constant';
import { EVENT_ENUM } from '@constant/event.enum';
import { AllConfigType } from '@config/config.type';
import { BaseDto } from '@core/dto/base.request.dto';
import {
  TRANSACTION_CONST,
  TRANSACTION_ACTION_ENUM,
} from '@components/transaction/transaction.constant';
import { Order } from '@database/schemas/order.model';
import { Product } from '@database/schemas/product.model';
import { ResponseBuilder } from '@utils/response-builder';
import { IdParamDto } from '@core/dto/param-id.request.dto';
import { Cache, CACHE_MANAGER } from '@nestjs/cache-manager';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { formatCurrency, generateRandomString } from '@helpers/string.helper';
import { CreateOrderRequestDto } from './dto/request/create-order.request.dto';
import { CancelOrderRequestDto } from './dto/request/cancel-order.request.dto';
import { GetListOrderRequestDto } from './dto/request/get-list-order.request.dto';
import { ShippingOrderRequestDto } from './dto/request/shipping-order.request.dto';
import { BusinessException } from '@core/exception-filter/business-exception.filter';
import { GetDetailOrderResponseDto } from './dto/response/order-detail.response.dto';
import { CartRepositoryInterface } from '@database/repository/cart/cart.repository.interface';
import { UserRepositoryInterface } from '@database/repository/user/user.repository.interface';
import { VnPayReturnRequestDto } from '@components/order/dto/request/vnpay-return.request.dto';
import { RejectOrderRequestDto } from '@components/order/dto/request/reject-order.request.dto';
import { OrderRepositoryInterface } from '@database/repository/order/order.repository.interface';
import { TransactionRepositoryInterface } from '@database/repository/transaction/transaction.repository.interface';
import { StatisticOrderByStatusResponseDto } from '@components/order/dto/response/statistic-order-by-status.response.dto';

@Injectable()
export class OrderService {
  private readonly logger = new Logger(OrderService.name);
  constructor(
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,

    private readonly i18n: I18nService,

    @InjectConnection()
    private readonly connection: Connection,

    private readonly eventEmitter: EventEmitter2,

    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,

    @Inject('CartRepositoryInterface')
    private readonly cartRepository: CartRepositoryInterface,

    @Inject('OrderRepositoryInterface')
    private readonly orderRepository: OrderRepositoryInterface,

    private readonly configService: ConfigService<AllConfigType>,

    @Inject('TransactionRepositoryInterface')
    private readonly transactionRepository: TransactionRepositoryInterface,
  ) {}

  async create(request: CreateOrderRequestDto) {
    const { user, userId, cartDetails } = request;

    const totalOrdersPending = await this.orderRepository.countDocuments({
      user: userId,
      status: ORDER_STATUS_ENUM.PENDING,
    });

    // tối đa 10 đơn ở trạng thái chờ xác nhận
    if (totalOrdersPending >= ORDER_CONST.MAX_ORDER_PENDING) {
      throw new BusinessException(
        this.i18n.translate('error.MAX_ORDER_PENDING', {
          args: { max: ORDER_CONST.MAX_ORDER_PENDING },
        }),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const cart = await this.cartRepository
      .findOne({ user: userId })
      .populate<{
        cartDetails: Array<{
          product: Product;
          quantity: number;
          _id: Types.ObjectId;
        }>;
      }>({
        path: 'cartDetails.product',
      })
      .lean();

    const cartDetailsExist = map(cart?.cartDetails, '_id')?.map(String);
    const sameCartDetails = intersection(cartDetails, cartDetailsExist);

    if (sameCartDetails.length !== cartDetails.length) {
      throw new BusinessException(
        this.i18n.translate('error.PRODUCT_NOT_IN_CART'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const orderDetails = cart.cartDetails.filter((item) =>
      sameCartDetails.includes(item._id.toString()),
    );

    const totalPriceOrder = orderDetails.reduce(
      (total, item) => total + item.product.price * item.quantity,
      0,
    );

    if (
      request.paymentMethod === PAYMENT_METHOD_ENUM.PREPAID &&
      (user?.accountBalance ?? 0) < totalPriceOrder
    ) {
      throw new BusinessException(
        this.i18n.translate('error.AMOUNT_BALANCE_NOT_ENOUGH'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const orderData = {
      code: generateRandomString(6),
      user: userId,
      note: request.note,
      phone: request.phone,
      address: request.address,
      recipientName: request.recipientName,
      totalPrice: totalPriceOrder,
      paymentMethod: request.paymentMethod,
      orderDetails: orderDetails.map((item) => ({
        quantity: item.quantity,
        price: item.product.price,
        product: item.product._id,
        totalPrice: item.product.price * item.quantity,
      })),
      orderHistories: [
        {
          action: ORDER_HISTORY_ACTION_ENUM.CREATED,
          createdAt: new Date(),
        },
      ],
    };

    const session = await this.connection.startSession();
    session.startTransaction();

    try {
      const newOrder = await this.orderRepository.create(orderData, {
        session,
      });

      await this.cartRepository.updateOne(
        { user: userId },
        {
          $pull: { cartDetails: { _id: { $in: sameCartDetails } } },
        },
        { session },
      );

      let urlPayment = '';

      if (orderData.paymentMethod === PAYMENT_METHOD_ENUM.BANK) {
        urlPayment = this.createVnPayUrl(orderData as Order);

        newOrder.urlPayment = urlPayment;
        await newOrder.save({ session });
      } else if (orderData.paymentMethod === PAYMENT_METHOD_ENUM.PREPAID) {
        await this.transactionRepository.create(
          {
            user: userId,
            code: generateRandomString(6),
            amount: totalPriceOrder,
            balanceBefore: user?.accountBalance,
            action: TRANSACTION_ACTION_ENUM.PAYMENT,
            balanceAfter: user?.accountBalance - totalPriceOrder,
            note: `Thanh toán thành công đơn hàng [${orderData?.code}] ${formatCurrency(totalPriceOrder)} VNĐ`,
          },
          { session },
        );

        await this.userRepository.updateOne(
          { _id: userId },
          { $inc: { accountBalance: -totalPriceOrder } },
          { session },
        );

        newOrder.paymentStatus = PAYMENT_STATUS_ENUM.PAID;
        await newOrder.save({ session });
      }

      await session.commitTransaction();

      return new ResponseBuilder({ urlPayment })
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(this.i18n.translate('message.ORDER_SUCCESS'))
        .build();
    } catch (error) {
      this.logger.error(
        `[CREATE ORDER ERROR]: ${error} - ${JSON.stringify(error)}`,
      );
      await session.abortTransaction();
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.INTERNAL_SERVER_ERROR)
        .withMessage(this.i18n.translate('error.INTERNAL_SERVER_ERROR'))
        .build();
    } finally {
      await session.endSession();
    }
  }

  async detail(request: IdParamDto) {
    const { id, userId, user } = request;

    const isUser = user?.role === USER_ROLE_ENUM.USER;

    const order = await this.orderRepository
      .findOne({
        _id: id,
        ...(isUser ? { user: userId } : {}),
      })
      .populate([
        {
          path: 'user',
          select: 'email fullname',
        },
        {
          path: 'shipper',
          select: 'email fullname phone',
        },
        {
          path: 'orderDetails.product',
          populate: {
            path: 'category',
            select: 'name slug image description',
          },
        },
      ]);

    if (isEmpty(order)) {
      throw new BusinessException(
        this.i18n.translate('error.ORDER_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const response = plainToInstance(GetDetailOrderResponseDto, order, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async cancel(request: CancelOrderRequestDto) {
    const { id, user, userId, description } = request;

    const isUser = user?.role === USER_ROLE_ENUM.USER;
    const order = await this.orderRepository.findOne({
      _id: id,
      ...(isUser ? { user: userId } : {}),
    });

    if (isEmpty(order)) {
      throw new BusinessException(
        this.i18n.translate('error.ORDER_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    if (
      ![
        ORDER_STATUS_ENUM.PENDING,
        ORDER_STATUS_ENUM.SHIPPING,
        ORDER_STATUS_ENUM.PREPARING,
      ].includes(order.status)
    ) {
      throw new BusinessException(
        this.i18n.translate('error.STATUS_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const session = await this.connection.startSession();
    session.startTransaction();

    try {
      await this.orderRepository.updateOne(
        { _id: id },
        {
          urlPayment: '',
          status: ORDER_STATUS_ENUM.CANCELED,
          $push: {
            orderHistories: {
              description,
              createdAt: new Date(),
              action: ORDER_HISTORY_ACTION_ENUM.CANCELLED,
            },
          },
        },
        { session },
      );

      if (
        [PAYMENT_METHOD_ENUM.BANK, PAYMENT_METHOD_ENUM.PREPAID].includes(
          order.paymentMethod,
        ) &&
        order.paymentStatus === PAYMENT_STATUS_ENUM.PAID
      ) {
        await this.transactionRepository.create(
          {
            user: userId,
            code: generateRandomString(6),
            amount: order.totalPrice,
            balanceBefore: user?.accountBalance,
            balanceAfter: user?.accountBalance + order.totalPrice,
            action: TRANSACTION_ACTION_ENUM.REFUND,
            note: `Hoàn tiền thành công ${formatCurrency(order?.totalPrice)} VNĐ do hủy đơn hàng [${order?.code}]`,
          },
          { session },
        );

        await this.userRepository.updateOne(
          { _id: userId },
          { $inc: { accountBalance: order.totalPrice } },
          { session },
        );
      }

      await session.commitTransaction();
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(this.i18n.translate('message.SUCCESS'))
        .build();
    } catch (error) {
      this.logger.error(
        `[CANCEL ORDER ERROR]: ${error} - ${JSON.stringify(error)}`,
      );
      await session.abortTransaction();
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.INTERNAL_SERVER_ERROR)
        .withMessage(this.i18n.translate('error.INTERNAL_SERVER_ERROR'))
        .build();
    } finally {
      await session.endSession();
    }
  }

  async reject(request: RejectOrderRequestDto) {
    const { id, description } = request;

    const order = await this.orderRepository.findOne({
      _id: id,
    });

    if (isEmpty(order)) {
      throw new BusinessException(
        this.i18n.translate('error.ORDER_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    if (![ORDER_STATUS_ENUM.PENDING].includes(order.status)) {
      throw new BusinessException(
        this.i18n.translate('error.STATUS_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const session = await this.connection.startSession();
    session.startTransaction();

    try {
      await this.orderRepository.updateOne(
        { _id: id },
        {
          urlPayment: '',
          status: ORDER_STATUS_ENUM.REJECTED,
          $push: {
            orderHistories: {
              description,
              createdAt: new Date(),
              action: ORDER_HISTORY_ACTION_ENUM.REJECTED,
            },
          },
        },
        { session },
      );

      if (
        [PAYMENT_METHOD_ENUM.BANK, PAYMENT_METHOD_ENUM.PREPAID].includes(
          order.paymentMethod,
        ) &&
        order.paymentStatus === PAYMENT_STATUS_ENUM.PAID
      ) {
        const user = await this.userRepository.findOne({
          _id: order.user,
        });

        await this.transactionRepository.create(
          {
            user: order.user,
            code: generateRandomString(6),
            amount: order.totalPrice,
            balanceBefore: user?.accountBalance,
            balanceAfter: user?.accountBalance + order.totalPrice,
            action: TRANSACTION_ACTION_ENUM.REFUND,
            note: `Hoàn thành công ${formatCurrency(order?.totalPrice)} VNĐ do đơn hàng [${order?.code}] bị từ chối`,
          },
          { session },
        );

        await this.userRepository.updateOne(
          { _id: order.user },
          { $inc: { accountBalance: order.totalPrice } },
          { session },
        );
      }

      await session.commitTransaction();
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(this.i18n.translate('message.SUCCESS'))
        .build();
    } catch (error) {
      this.logger.error(
        `[REJECT ORDER ERROR]: ${error} - ${JSON.stringify(error)}`,
      );
      await session.abortTransaction();
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.INTERNAL_SERVER_ERROR)
        .withMessage(this.i18n.translate('error.INTERNAL_SERVER_ERROR'))
        .build();
    } finally {
      await session.endSession();
    }
  }

  async listForAdmin(request: GetListOrderRequestDto) {
    const { data, total } = await this.orderRepository.list(request);

    const response = plainToInstance(GetDetailOrderResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      meta: { total, page: request.page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async listForUser(request: GetListOrderRequestDto) {
    const { data, total } = await this.orderRepository.list(
      request,
      USER_ROLE_ENUM.USER,
    );

    const response = plainToInstance(GetDetailOrderResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      meta: { total, page: request.page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async listForShipper(request: GetListOrderRequestDto) {
    const { data, total } = await this.orderRepository.list(
      request,
      USER_ROLE_ENUM.SHIPPER,
    );

    const response = plainToInstance(GetDetailOrderResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      meta: { total, page: request.page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async confirm(request: IdParamDto) {
    const { id } = request;

    const order = await this.orderRepository.findById(id);
    if (isEmpty(order)) {
      throw new BusinessException(
        this.i18n.translate('error.ORDER_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    if (order.status !== ORDER_STATUS_ENUM.PENDING) {
      throw new BusinessException(
        this.i18n.translate('error.STATUS_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const session = await this.connection.startSession();
    session.startTransaction();

    try {
      await this.orderRepository.updateOne(
        { _id: id },
        {
          urlPayment: '',
          status: ORDER_STATUS_ENUM.PREPARING,
          $push: {
            orderHistories: {
              action: ORDER_HISTORY_ACTION_ENUM.CONFIRMED,
              createdAt: new Date(),
            },
          },
        },
        { session },
      );

      await session.commitTransaction();
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(this.i18n.translate('message.SUCCESS'))
        .build();
    } catch (error) {
      this.logger.error(
        `[CONFIRM ORDER ERROR]: ${error} - ${JSON.stringify(error)}`,
      );
      await session.abortTransaction();
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.INTERNAL_SERVER_ERROR)
        .withMessage(this.i18n.translate('error.INTERNAL_SERVER_ERROR'))
        .build();
    } finally {
      await session.endSession();
    }
  }

  async shipping(request: ShippingOrderRequestDto) {
    const { id, shipperId } = request;

    const shipper = await this.userRepository.findOne({
      _id: shipperId,
      role: USER_ROLE_ENUM.SHIPPER,
    });
    if (isEmpty(shipper)) {
      throw new BusinessException(
        this.i18n.translate('error.SHIPPER_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const order = await this.orderRepository.findById(id);
    if (isEmpty(order)) {
      throw new BusinessException(
        this.i18n.translate('error.ORDER_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    if (order.status !== ORDER_STATUS_ENUM.PREPARING) {
      throw new BusinessException(
        this.i18n.translate('error.STATUS_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const session = await this.connection.startSession();
    session.startTransaction();

    try {
      await this.orderRepository.updateOne(
        { _id: id },
        {
          status: ORDER_STATUS_ENUM.SHIPPING,
          shipper: shipper._id,
          $push: {
            orderHistories: {
              action: ORDER_HISTORY_ACTION_ENUM.DELIVERING,
              createdAt: new Date(),
            },
          },
        },
        { session },
      );

      await session.commitTransaction();
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(this.i18n.translate('message.SUCCESS'))
        .build();
    } catch (error) {
      this.logger.error(
        `[SHIPPING ORDER ERROR]: ${error} - ${JSON.stringify(error)}`,
      );
      await session.abortTransaction();
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.INTERNAL_SERVER_ERROR)
        .withMessage(this.i18n.translate('error.INTERNAL_SERVER_ERROR'))
        .build();
    } finally {
      await session.endSession();
    }
  }

  async delivered(request: IdParamDto) {
    const { id, user, userId } = request;

    const filter = { _id: id } as { _id: string; shipper?: string };

    if (user.role === USER_ROLE_ENUM.SHIPPER) {
      filter.shipper = userId;
    }

    const order = await this.orderRepository.findOne(filter);

    if (isEmpty(order)) {
      throw new BusinessException(
        this.i18n.translate('error.ORDER_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    if (order.status !== ORDER_STATUS_ENUM.SHIPPING) {
      throw new BusinessException(
        this.i18n.translate('error.STATUS_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const session = await this.connection.startSession();
    session.startTransaction();

    try {
      await this.orderRepository.updateOne(
        { _id: id },
        {
          completedAt: new Date(),
          status: ORDER_STATUS_ENUM.DELIVERED,
          $push: {
            orderHistories: {
              action: ORDER_HISTORY_ACTION_ENUM.COMPLETED,
              createdAt: new Date(),
            },
          },
        },
        { session },
      );

      await session.commitTransaction();

      this.eventEmitter.emit(EVENT_ENUM.SYNC_DATA_ORDER_SUMMARY, order);
      this.eventEmitter.emit(EVENT_ENUM.UPDATE_SOLD_QUANTITY_PRODUCT, order);

      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(this.i18n.translate('message.SUCCESS'))
        .build();
    } catch (error) {
      this.logger.error(
        `[DELIVER ORDER ERROR]: ${error} - ${JSON.stringify(error)}`,
      );
      await session.abortTransaction();
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.INTERNAL_SERVER_ERROR)
        .withMessage(this.i18n.translate('error.INTERNAL_SERVER_ERROR'))
        .build();
    } finally {
      await session.endSession();
    }
  }

  async reset(request: BaseDto) {
    const { userId } = request;
    await this.orderRepository.deleteMany({ user: userId });
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async statisticByStatus(request: BaseDto) {
    const result = await this.orderRepository.statisticByStatus(request);

    const response = plainToInstance(
      StatisticOrderByStatusResponseDto,
      result,
      {
        excludeExtraneousValues: true,
      },
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async callbackVnPay(
    request: VnPayReturnRequestDto,
  ): Promise<{ result: boolean; token?: string }> {
    let vnp_Params = request;

    const VnPayConfig = this.configService.get('vnPay', {
      infer: true,
    });

    const secretKey = VnPayConfig.hashSecret;
    const secureHash = vnp_Params['vnp_SecureHash'];

    delete vnp_Params['vnp_SecureHash'];
    delete vnp_Params['vnp_SecureHashType'];

    vnp_Params = this.sortObject(vnp_Params);

    const hmac = crypto.createHmac('sha512', secretKey);
    const signData = queryString.stringify(vnp_Params, { encode: false });
    const signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');

    if (secureHash === signed) {
      const orderCode = vnp_Params['vnp_TxnRef'];

      const orderPay = await this.orderRepository.findOne({
        code: orderCode,
        paymentStatus: PAYMENT_STATUS_ENUM.PAID,
      });

      if (!isEmpty(orderPay)) {
        return { result: false };
        // return new ResponseBuilder()
        //   .withCode(ResponseCodeEnum.BAD_REQUEST)
        //   .withMessage(this.i18n.translate('error.ORDER_ALREADY_PAID'))
        //   .build();
      }

      const session = await this.connection.startSession();
      session.startTransaction();

      try {
        const orderSave = await this.orderRepository.findOne({
          code: orderCode,
        });

        const user = await this.userRepository.findOne({
          _id: orderSave.user,
        });

        if (
          [ORDER_STATUS_ENUM.CANCELED, ORDER_STATUS_ENUM.REJECTED].includes(
            orderSave.status,
          )
        ) {
          await this.userRepository.updateOne(
            { _id: orderSave.user },
            { $inc: { accountBalance: orderSave.totalPrice } },
            { session },
          );

          await this.transactionRepository.create(
            {
              user: orderSave.user,
              amount: orderSave.totalPrice,
              code: generateRandomString(6),
              balanceBefore: user?.accountBalance,
              action: TRANSACTION_ACTION_ENUM.REFUND,
              balanceAfter: user?.accountBalance + orderSave.totalPrice,
              note: `Hoàn tiền đơn hàng [${orderSave?.code}] bị huỷ trước khi thanh toán ${formatCurrency(orderSave?.totalPrice)} VNĐ`,
            },
            { session },
          );
        } else {
          await this.orderRepository.updateOne(
            { code: orderCode },
            {
              paymentStatus: PAYMENT_STATUS_ENUM.PAID,
              $push: {
                orderHistories: {
                  createdAt: new Date(),
                  action: ORDER_HISTORY_ACTION_ENUM.PAYMENT_SUCCESS,
                },
              },
            },
            { session },
          );

          await this.transactionRepository.create(
            {
              user: orderSave.user,
              code: generateRandomString(6),
              amount: orderSave.totalPrice,
              balanceBefore: user?.accountBalance,
              balanceAfter: user?.accountBalance,
              action: TRANSACTION_ACTION_ENUM.PAYMENT,
              note: `Thanh toán thành công đơn hàng [${orderSave?.code}] ${formatCurrency(orderSave?.totalPrice)} VNĐ`,
            },
            { session },
          );
        }

        await session.commitTransaction();

        const token = generateRandomString(20);
        await this.cacheManager.set(
          token,
          true,
          TRANSACTION_CONST.VERIFY_TRANSACTION_TOKEN_EXPIRED_TIME,
        );

        return { result: true, token };
        // return new ResponseBuilder()
        //   .withCode(ResponseCodeEnum.SUCCESS)
        //   .withMessage(this.i18n.translate('message.PAYMENT_SUCCESS'))
        //   .build();
      } catch (error) {
        this.logger.error(
          `[CALLBACK VNPAY ERROR]: ${error} - ${JSON.stringify(error)}`,
        );
        await session.abortTransaction();
        return { result: false };
      } finally {
        await session.endSession();
      }
    } else {
      return { result: false };
      // return new ResponseBuilder()
      //   .withCode(ResponseCodeEnum.BAD_REQUEST)
      //   .withMessage(this.i18n.translate('error.PAYMENT_FAILED'))
      //   .build();
    }
  }

  private createVnPayUrl(order: Order): string {
    const { code, totalPrice } = order;

    const locale = 'vn';
    const currCode = 'VND';
    const createDate = moment().format('YYYYMMDDHHmmss');

    const VnPayConfig = this.configService.get('vnPay', {
      infer: true,
    });

    let vnpUrl = VnPayConfig.VnPayUrl;
    const tmnCode = VnPayConfig.tmnCode;
    const returnUrl = VnPayConfig.returnUrl;
    const secretKey = VnPayConfig.hashSecret;

    let VnPayParams = {};

    VnPayParams['vnp_TxnRef'] = code;
    VnPayParams['vnp_Command'] = 'pay';
    VnPayParams['vnp_Locale'] = locale;
    VnPayParams['vnp_TmnCode'] = tmnCode;
    VnPayParams['vnp_Version'] = '2.1.0';
    VnPayParams['vnp_CurrCode'] = currCode;
    VnPayParams['vnp_OrderType'] = 'other';
    VnPayParams['vnp_IpAddr'] = '127.0.0.1';
    VnPayParams['vnp_ReturnUrl'] = returnUrl;
    VnPayParams['vnp_CreateDate'] = createDate;
    VnPayParams['vnp_Amount'] = totalPrice * 100;
    VnPayParams['vnp_OrderInfo'] = `Thanh toan don hang: ${code}`;

    VnPayParams = this.sortObject(VnPayParams);

    const hmac = crypto.createHmac('sha512', secretKey);
    const signData = queryString.stringify(VnPayParams, { encode: false });
    const signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');

    VnPayParams['vnp_SecureHash'] = signed;
    vnpUrl += '?' + queryString.stringify(VnPayParams, { encode: false });

    return vnpUrl;
  }

  private sortObject(obj: any): any {
    const str = [];
    const sorted = {};

    let key;
    for (key in obj) {
      if (obj.hasOwnProperty(key)) {
        str.push(encodeURIComponent(key));
      }
    }

    str.sort();

    for (key = 0; key < str.length; key++) {
      sorted[str[key]] = encodeURIComponent(obj[str[key]]).replace(/%20/g, '+');
    }

    return sorted;
  }
}
