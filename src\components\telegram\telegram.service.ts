import * as fs from 'fs';
import axios from 'axios';
import * as path from 'path';
import * as moment from 'moment';
import { Telegraf } from 'telegraf';
import * as CryptoJS from 'crypto-js';
import { ConfigService } from '@nestjs/config';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';

import { AllConfigType } from '@config/config.type';
import { RequestLoggingType } from '@components/types/request-logging.type';

@Injectable()
export class TelegramService implements OnModuleInit {
  private readonly logger = new Logger(TelegramService.name);

  // Constants for configurable settings
  private readonly MAX_LOGS_PER_MESSAGE = 5; // Số dòng log tối đa trong một message
  private readonly MAX_MESSAGES_PER_MINUTE = 5; // Số message tối đa gửi trong một phút
  private readonly TELEGRAM_TIMEOUT_MS = 10000; // Timeout cho API Telegram
  private readonly encryptionKey: string;

  private logDirectory: string;
  private readonly chatId: string;
  private readonly logsBot: string;
  private readonly backupBot: Telegraf;
  private readonly backupChatId: string;
  private lastProcessedPosition: number = 0;
  private lastProcessedPositionFile: string;

  constructor(private readonly configService: ConfigService<AllConfigType>) {
    this.logDirectory = path.resolve(process.cwd(), 'logs');

    if (!fs.existsSync(this.logDirectory)) {
      fs.mkdirSync(this.logDirectory, { recursive: true });
    }

    const appConfig = this.configService.get('app', {
      infer: true,
    });

    // bot logs
    this.chatId = appConfig.telegramChatId;
    this.logsBot = appConfig.telegramLogsBotToken;

    // bot backup
    this.backupChatId = appConfig.telegramBackupChatId;
    this.encryptionKey = appConfig.backupEncryptionKey;
    this.backupBot = new Telegraf(appConfig.telegramBackupBotToken);

    this.lastProcessedPositionFile = path.join(
      this.logDirectory,
      '.telegram-last-position',
    );
  }

  async onModuleInit() {
    await this.loadLastProcessedPosition();
  }

  private async loadLastProcessedPosition() {
    try {
      if (fs.existsSync(this.lastProcessedPositionFile)) {
        const data = await fs.promises.readFile(
          this.lastProcessedPositionFile,
          'utf8',
        );
        const savedState = JSON.parse(data);

        const today = moment().format('YYYY-MM-DD');
        if (savedState.date === today) {
          this.lastProcessedPosition = savedState.position || 0;
          this.logger.log(
            `Restored last processed position: ${this.lastProcessedPosition} for date: ${today}`,
          );
        } else {
          this.lastProcessedPosition = 0;
          this.logger.log(
            `New day detected, resetting processed position to 0`,
          );
        }
      }
    } catch (error) {
      this.logger.error('Error loading last processed position', error);
      this.lastProcessedPosition = 0;
    }
  }

  private async saveLastProcessedPosition(position: number) {
    try {
      const today = moment().format('YYYY-MM-DD');
      const data = JSON.stringify({
        date: today,
        position: position,
      });
      await fs.promises.writeFile(this.lastProcessedPositionFile, data, 'utf8');
    } catch (error) {
      this.logger.error('Error saving last processed position', error);
    }
  }

  async sendLogsToTelegram() {
    try {
      const today = moment().format('YYYY-MM-DD');
      const logFilePath = path.join(this.logDirectory, `request-${today}.log`);

      if (!fs.existsSync(logFilePath)) {
        return;
      }

      const stats = fs.statSync(logFilePath);
      const fileSize = stats.size;

      if (this.lastProcessedPosition >= fileSize) {
        return;
      }

      const newLogs = await this.readNewLogsFromFile(
        logFilePath,
        this.lastProcessedPosition,
        fileSize,
      );

      if (newLogs.length === 0) {
        return;
      }

      // Sắp xếp log theo thời gian
      const sortedLogs = this.sortLogsByTimestamp(newLogs);

      // Xử lý và gửi log theo batch
      const newPosition = await this.processAndSendLogBatches(
        sortedLogs,
        fileSize,
      );

      // Cập nhật vị trí xử lý cuối cùng
      if (newPosition > this.lastProcessedPosition) {
        this.lastProcessedPosition = newPosition;
        await this.saveLastProcessedPosition(newPosition);
      }
    } catch (error) {
      this.logger.error('Error sending logs to Telegram:', error);
    }
  }

  private sortLogsByTimestamp(
    logs: RequestLoggingType[],
  ): RequestLoggingType[] {
    return logs.sort((a, b) => {
      const timeA = new Date(a.timestamp).getTime();
      const timeB = new Date(b.timestamp).getTime();
      return timeA - timeB;
    });
  }

  private async readNewLogsFromFile(
    filePath: string,
    startPosition: number,
    endPosition: number,
  ): Promise<RequestLoggingType[]> {
    return new Promise((resolve, reject) => {
      try {
        const logEntries: RequestLoggingType[] = [];
        const stream = fs.createReadStream(filePath, {
          encoding: 'utf-8',
          start: startPosition,
          end: endPosition - 1,
        });

        let buffer = '';

        stream.on('data', (chunk) => {
          buffer += chunk;

          let lineEnd = buffer.indexOf('\n');
          while (lineEnd !== -1) {
            const line = buffer.substring(0, lineEnd).trim();
            buffer = buffer.substring(lineEnd + 1);

            if (line) {
              // Kiểm tra xem dòng có phải là JSON hợp lệ trước khi parse
              if (this.isValidJson(line)) {
                try {
                  const entry = JSON.parse(line);
                  logEntries.push(entry);
                } catch (e) {
                  this.logger.error(
                    `Error parsing log entry: ${line}`,
                    e.stack,
                  );
                }
              } else {
                this.logger.error(`Invalid JSON format in log entry: ${line}`);
              }
            }

            lineEnd = buffer.indexOf('\n');
          }
        });

        stream.on('end', () => {
          const remaining = buffer.trim();
          if (remaining) {
            // Kiểm tra phần còn lại trong buffer
            if (this.isValidJson(remaining)) {
              try {
                const entry = JSON.parse(remaining);
                logEntries.push(entry);
              } catch (e) {
                this.logger.error(
                  `Error parsing log entry: ${remaining}`,
                  e.stack,
                );
              }
            } else {
              this.logger.error(
                `Invalid JSON format in log entry: ${remaining}`,
              );
            }
          }

          resolve(logEntries);
        });

        stream.on('error', (err) => {
          this.logger.error(
            `Stream error while reading file ${filePath}`,
            err.stack,
          );
          reject(err);
        });
      } catch (error) {
        this.logger.error(
          `Unexpected error in readNewLogsFromFile`,
          error.stack,
        );
        reject(error);
      }
    });
  }

  private async processAndSendLogBatches(
    logs: RequestLoggingType[],
    fileSize: number,
  ): Promise<number> {
    try {
      // Gom nhóm log thành các nhóm MAX_LOGS_PER_MESSAGE logs/message
      const groupedMessages: RequestLoggingType[][] = [];
      for (let i = 0; i < logs.length; i += this.MAX_LOGS_PER_MESSAGE) {
        const group = logs.slice(i, i + this.MAX_LOGS_PER_MESSAGE);
        groupedMessages.push(group);
      }

      // Giới hạn số lượng message gửi trong mỗi phút
      const messagesToSend = groupedMessages.slice(
        0,
        this.MAX_MESSAGES_PER_MINUTE,
      );

      if (messagesToSend.length === 0) {
        return this.lastProcessedPosition;
      }

      // Gửi các nhóm log
      for (const logGroup of messagesToSend) {
        await this.sendLogGroupToTelegram(logGroup);
      }

      // Tính toán vị trí mới để lưu
      // Nếu đã xử lý hết logs, thì trả về fileSize
      // Nếu chưa xử lý hết, trả về vị trí cuối cùng đã xử lý
      if (messagesToSend.length < groupedMessages.length) {
        // Không đủ quota để gửi hết logs, chỉ cập nhật đến nhóm cuối cùng đã gửi
        const processedLogs = messagesToSend.flat();
        const lastProcessedLog = logs.indexOf(
          processedLogs[processedLogs.length - 1],
        );

        // Tính toán vị trí tương đối trong file dựa vào phần trăm đã xử lý
        const processedPercentage = (lastProcessedLog + 1) / logs.length;
        const newPosition =
          this.lastProcessedPosition +
          Math.floor(
            (fileSize - this.lastProcessedPosition) * processedPercentage,
          );
        return newPosition;
      } else {
        // Đã xử lý hết logs
        return fileSize;
      }
    } catch (error) {
      this.logger.error('Error processing log batches', error);
      return this.lastProcessedPosition;
    }
  }

  private async sendLogGroupToTelegram(
    logGroup: RequestLoggingType[],
  ): Promise<void> {
    try {
      // Tạo message từ nhóm log
      let message = `📝 User Activity Logs (Batch of ${logGroup.length})\n\n`;

      for (let i = 0; i < logGroup.length; i++) {
        const entry = logGroup[i];
        message += `--- Log ${i + 1} ---\n`;
        message += `📅 Date: ${entry.timestamp}\n`;
        message += `👤 User: ${entry.email}\n`;
        message += `🌐 IP: ${entry.ip}\n`;
        message += `📍 Path: ${entry.url}\n`;
        message += `🔧 Method: ${entry.method}\n`;
        message += `📊 Status: ${entry.statusCode}\n`;
        message += `🕒 Time: ${entry.responseTime}\n`;

        if (entry.hasBearer !== undefined) {
          message += `🔒 Auth: ${entry.hasBearer ? 'Bearer' : 'No Bearer'}\n`;
        }

        if (i < logGroup.length - 1) {
          message += '\n';
        }
      }

      await axios.post(
        `https://api.telegram.org/bot${this.logsBot}/sendMessage`,
        {
          text: message,
          chat_id: this.chatId,
        },
        {
          timeout: this.TELEGRAM_TIMEOUT_MS,
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to send log group to Telegram: ${JSON.stringify(logGroup.map((log) => log.url))}`,
        error,
      );
    }
  }

  private isValidJson(str: string): boolean {
    if (!str || typeof str !== 'string') return false;
    return str.startsWith('{') && str.endsWith('}');
  }

  /**
   * Mã hóa file backup sử dụng AES
   * @param filePath Đường dẫn đến file cần mã hóa
   * @returns Đường dẫn đến file đã mã hóa
   */
  async encryptBackupFile(filePath: string): Promise<string> {
    try {
      this.logger.log(`Encrypting backup file: ${filePath}`);

      // Đọc file backup
      const fileData = await fs.promises.readFile(filePath);

      // Mã hóa dữ liệu file bằng AES
      const encryptedData = CryptoJS.AES.encrypt(
        fileData.toString('base64'),
        this.encryptionKey,
      ).toString();

      // Tạo file mã hóa mới
      const encryptedFilePath = `${filePath}.enc`;
      await fs.promises.writeFile(encryptedFilePath, encryptedData);

      this.logger.log(`Encrypted backup saved to: ${encryptedFilePath}`);
      return encryptedFilePath;
    } catch (error) {
      this.logger.error(`Error encrypting backup file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Gửi file backup đã mã hóa lên Telegram
   * @param filePath Đường dẫn đến file đã mã hóa
   */
  async sendBackupToTelegram(filePath: string): Promise<void> {
    try {
      this.logger.log(`Sending encrypted backup to Telegram: ${filePath}`);

      // Gửi thông báo trước khi gửi file
      const date = moment().format('DD/MM/YYYY HH:mm:ss');
      await this.backupBot.telegram.sendMessage(
        this.backupChatId,
        `🔒 Database Backup (${date})\nEncrypted with AES`,
      );

      // Gửi file backup đã mã hóa
      await this.backupBot.telegram.sendDocument(this.backupChatId, {
        source: filePath,
      });

      this.logger.log('Backup file sent to Telegram successfully');
    } catch (error) {
      this.logger.error(`Error sending backup to Telegram: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xử lý quy trình mã hóa và gửi file backup lên Telegram
   * @param backupFilePath Đường dẫn đến file backup
   */
  async processAndSendBackup(backupFilePath: string): Promise<void> {
    try {
      // Kiểm tra file tồn tại
      if (!fs.existsSync(backupFilePath)) {
        this.logger.error(`Backup file not found: ${backupFilePath}`);
        return;
      }

      // Mã hóa file backup
      const encryptedFilePath = await this.encryptBackupFile(backupFilePath);

      // Gửi file đã mã hóa lên Telegram
      await this.sendBackupToTelegram(encryptedFilePath);

      // Xóa file đã mã hóa sau khi gửi (tùy chọn)
      // await fs.promises.unlink(encryptedFilePath);
    } catch (error) {
      this.logger.error(
        `Error processing and sending backup: ${error.message}`,
      );
    }
  }
}
