import * as mongoose from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import {
  ORDER_STATUS_ENUM,
  PAYMENT_METHOD_ENUM,
  PAYMENT_STATUS_ENUM,
  ORDER_HISTORY_ACTION_ENUM,
} from '@components/order/order.constant';
import { BaseModel } from '@core/schema/base.model';

@Schema({
  timestamps: true,
  collation: { locale: 'vi' },
})
export class OrderHistory extends BaseModel {
  @Prop({
    type: Number,
    enum: ORDER_HISTORY_ACTION_ENUM,
    required: true,
  })
  action: ORDER_HISTORY_ACTION_ENUM;

  @Prop({
    type: String,
    required: false,
  })
  description: string;
}

export const OrderHistorySchema = SchemaFactory.createForClass(OrderHistory);

export type OrderHistoryDocument = OrderHistory & mongoose.Document;

@Schema({
  timestamps: true,
  collation: { locale: 'vi' },
})
export class OrderDetail extends BaseModel {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true,
  })
  product: string;

  @Prop({
    type: Number,
    required: true,
    min: 1,
  })
  quantity: number;

  @Prop({
    type: Number,
    required: true,
  })
  price: number;

  @Prop({
    type: Number,
    required: true,
  })
  totalPrice: number;
}

export const OrderDetailSchema = SchemaFactory.createForClass(OrderDetail);

export type OrderDetailDocument = OrderDetail & mongoose.Document;

@Schema({
  timestamps: true,
  collection: 'orders',
  collation: { locale: 'vi' },
})
export class Order extends BaseModel {
  @Prop({
    type: String,
    required: true,
    index: true,
  })
  code: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false,
    index: true,
  })
  user: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false,
    index: true,
  })
  shipper: string;

  @Prop({
    type: [OrderDetailSchema],
    required: false,
    default: [],
  })
  orderDetails?: OrderDetail[];

  @Prop({
    type: Number,
    required: true,
  })
  totalPrice: number;

  @Prop({
    type: String,
    required: true,
  })
  recipientName: string;

  @Prop({
    type: String,
    required: false,
  })
  phone: string;

  @Prop({
    type: String,
    required: true,
  })
  address: string;

  @Prop({
    type: String,
    required: false,
  })
  note: string;

  @Prop({
    type: Number,
    enum: PAYMENT_METHOD_ENUM,
    default: PAYMENT_METHOD_ENUM.COD,
    index: true,
  })
  paymentMethod: PAYMENT_METHOD_ENUM;

  @Prop({
    type: Number,
    enum: PAYMENT_STATUS_ENUM,
    default: PAYMENT_STATUS_ENUM.UNPAID,
    index: true,
  })
  paymentStatus: PAYMENT_STATUS_ENUM;

  @Prop({
    type: String,
    required: false,
  })
  urlPayment: string;

  @Prop({
    type: Date,
    required: false,
  })
  completedAt: Date;

  @Prop({
    type: Number,
    enum: ORDER_STATUS_ENUM,
    default: ORDER_STATUS_ENUM.PENDING,
    index: true,
  })
  status: ORDER_STATUS_ENUM;

  @Prop({
    type: [OrderHistorySchema],
    required: false,
    default: [],
  })
  orderHistories?: OrderHistory[];
}

export const OrderSchema = SchemaFactory.createForClass(Order);

// Tạo compound index cho các trường thường được query cùng nhau
OrderSchema.index({ status: 1, paymentMethod: 1, paymentStatus: 1 });
OrderSchema.index({ user: 1, status: 1 });
OrderSchema.index({ shipper: 1, status: 1 });
OrderSchema.index({ createdAt: -1 });

export type OrderDocument = Order & mongoose.Document;
