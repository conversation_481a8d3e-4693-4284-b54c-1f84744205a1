import {
  Put,
  Get,
  Body,
  Post,
  Param,
  Query,
  Delete,
  UseGuards,
  Controller,
  UseInterceptors,
} from '@nestjs/common';
import { isEmpty } from 'lodash';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { mergePayload } from '@utils/common';
import { RoleGuard } from '@core/guards/role.guard';
import { ProductService } from './product.service';
import { Roles } from '@core/decorators/roles.decorator';
import { Public } from '@core/decorators/public.decorator';
import { IdParamDto } from '@core/dto/param-id.request.dto';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { UpdateProductRequestDto } from './dto/request/update-product.request.dto';
import { CreateProductRequestDto } from './dto/request/create-product.request.dto';
import { GetListProductRequestDto } from './dto/request/get-list-product.request.dto';
import { GetDetailProductRequestDto } from './dto/request/get-detail-product.request.dto';

@ApiBearerAuth()
@Controller('products')
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Public()
  @Get('/')
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({
    tags: ['Product'],
    summary: 'Danh sách sản phẩm',
    description: 'Danh sách sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async list(@Query() query: GetListProductRequestDto) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.productService.list(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Post('/')
  @ApiOperation({
    tags: ['Product'],
    summary: 'Tạo mới sản phẩm',
    description: 'Tạo mới sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async create(@Body() payload: CreateProductRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.productService.create(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Put('/:id')
  @ApiOperation({
    tags: ['Product'],
    summary: 'Cập nhật thông tin sản phẩm',
    description: 'Cập nhật thông tin sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async update(
    @Param() param: IdParamDto,
    @Body() payload: UpdateProductRequestDto,
  ) {
    const { request, responseError } = mergePayload(payload, param);

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.productService.update(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Product'],
    summary: 'Xóa thông tin sản phẩm',
    description: 'Xóa thông tin sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async delete(@Param() param: IdParamDto) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.productService.delete(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Put('/:id/active')
  @ApiOperation({
    tags: ['Product'],
    summary: 'Mở bán sản phẩm',
    description: 'Mở bán sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async active(@Param() param: IdParamDto) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.productService.active(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Put('/:id/pause')
  @ApiOperation({
    tags: ['Product'],
    summary: 'Tạm dừng bán sản phẩm',
    description: 'Tạm dừng bán sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async pause(@Param() param: IdParamDto) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.productService.pause(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Get('/export')
  @ApiOperation({
    tags: ['Users'],
    summary: 'Xuất danh sách sản phẩm',
    description: 'Xuất danh sách sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async export(@Query() query: GetListProductRequestDto) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.productService.export(request);
  }

  @Public()
  @Get('/:id')
  @ApiOperation({
    tags: ['Product'],
    summary: 'Chi tiết sản phẩm',
    description: 'Chi tiết sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async detail(@Param() param: GetDetailProductRequestDto) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.productService.getDetail(request);
  }
}
