import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsMongo<PERSON>d,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { BaseDto } from '@core/dto/base.request.dto';

export class CreateChatRequestDto extends BaseDto {
  @ApiProperty()
  @IsString()
  @MinLength(2)
  @MaxLength(200)
  @IsNotEmpty()
  message: string;

  @IsString()
  @IsNotEmpty()
  @IsMongoId()
  sender: string;

  @IsString()
  @IsNotEmpty()
  @IsMongoId()
  receiver: string;
}
