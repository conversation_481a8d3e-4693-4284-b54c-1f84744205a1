import { Address } from '@database/schemas/address.model';
import { BaseInterfaceRepository } from '@core/repository/base.interface.repository';
import { GetMyAddressRequestDto } from '@components/address/dto/request/get-my-address.request.dto';
import { UpdateAddressRequestDto } from '@components/address/dto/request/update-address.request.dto';
import { CreateAddressRequestDto } from '@components/address/dto/request/create-address.request.dto';

export interface AddressRepositoryInterface
  extends BaseInterfaceRepository<Address> {
  createEntity(data: CreateAddressRequestDto): Address;

  updateEntity(entity: Address, data: UpdateAddressRequestDto): Address;

  list(
    request: GetMyAddressRequestDto,
  ): Promise<{ data: Address[]; total: number }>;
}
