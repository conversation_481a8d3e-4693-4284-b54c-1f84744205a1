import { OrderSummary } from '@database/schemas/order-summary.model';
import { BaseInterfaceRepository } from '@core/repository/base.interface.repository';
import { GetTopSellingProductRequestDto } from '@components/dashboard/dto/request/get-top-selling-product.request.dto';

export interface OrderSummaryRepositoryInterface
  extends BaseInterfaceRepository<OrderSummary> {
  getTopSellingProducts(request: GetTopSellingProductRequestDto): Promise<any>;
}
