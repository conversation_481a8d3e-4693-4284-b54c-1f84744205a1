import axios from 'axios';
import { I18nService } from 'nestjs-i18n';
import { ConfigService } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import { Injectable, Inject, Logger } from '@nestjs/common';

import { AllConfigType } from '@config/config.type';
import { BaseDto } from '@core/dto/base.request.dto';
import { promiseHelper } from '@helpers/promise.helper';
import { ResponseBuilder } from '@utils/response-builder';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { RequestChatbotRequestDto } from './dto/request/request-chatbot.request.dto';
import { BusinessException } from '@core/exception-filter/business-exception.filter';
import { ResponseChatbotResponseDto } from './dto/response/response-chatbot.response.dto';
import { ProductRepositoryInterface } from '@database/repository/product/product.repository.interface';
import { CategoryRepositoryInterface } from '@database/repository/category/category.repository.interface';

@Injectable()
export class ChatbotService {
  private readonly logger = new Logger(ChatbotService.name);

  private conversationContext = new Map<
    string,
    {
      lastQuery: string;
      timestamp: number;
      suggestedProducts?: any[];
    }
  >();

  private readonly openAIModel = 'gpt-4.1-nano';

  private readonly apiKeyGemini = this.configService.get('app.geminiApiKey', {
    infer: true,
  });

  private readonly apiKeyOpenAI = this.configService.get('app.openaiApiKey', {
    infer: true,
  });

  private readonly apiUrlOpenAI = 'https://api.openai.com/v1/chat/completions';

  private readonly apiUrlGemini =
    'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

  constructor(
    private readonly i18n: I18nService,

    private configService: ConfigService<AllConfigType>,

    @Inject('ProductRepositoryInterface')
    private readonly productRepository: ProductRepositoryInterface,

    @Inject('CategoryRepositoryInterface')
    private readonly categoryRepository: CategoryRepositoryInterface,
  ) {}

  private getConversationKey(userId?: string): string {
    return userId || 'anonymous';
  }

  private buildPrompt(prevContext: any, databaseData: any): string {
    return `
      Bạn là trợ lý AI cho trang web bán hàng. Hãy tuân theo các quy tắc sau:
      
      1. Bạn chỉ cung cấp thông tin về sản phẩm, danh mục, giá cả dựa trên dữ liệu được cung cấp.
      2. Nếu không có thông tin về sản phẩm nào đó, thông báo cho khách hàng biết và đề xuất sản phẩm tương tự.
      3. Luôn trả lời lịch sự, ngắn gọn và hữu ích.
      4. Đề xuất các sản phẩm liên quan khi thích hợp.
      5. Không tự tạo thông tin về sản phẩm không có trong dữ liệu.
      6. Không đề cập đến việc bạn là AI hay các giới hạn của mình.
      7. Trả lời bằng tiếng Việt.
      8. Giá sản phẩm luôn hiển thị theo định dạng VND với dấu phân cách.
      9. Nếu khách hàng hỏi về chính sách đổi trả, giao hàng, thanh toán, hãy cung cấp thông tin theo chính sách chuẩn của cửa hàng.
      10. Có thêm emoji vào cuối mỗi câu trả lời cho thân thiện hơn nhé lấy từ (🌟⭐🫶☘️😉🤗☺️😚💐🫰✨🎉)

      ${prevContext ? 'Đây là câu trả lời cho câu hỏi tiếp theo của người dùng về sản phẩm đã đề xuất trước đó.' : ''}
      
      Dữ liệu sản phẩm/danh mục từ database:
      ${JSON.stringify(databaseData)}
    `;
  }

  private updateContext(
    userId: string | undefined,
    context: string,
    suggestedProducts?: any[],
  ) {
    const key = this.getConversationKey(userId);
    this.conversationContext.set(key, {
      lastQuery: context,
      suggestedProducts,
      timestamp: Date.now(),
    });

    // Cleanup old contexts after 30 minutes
    setTimeout(
      () => {
        this.conversationContext.delete(key);
      },
      30 * 60 * 1000,
    );
  }

  private getContext(userId: string | undefined) {
    const key = this.getConversationKey(userId);
    const context = this.conversationContext.get(key);

    if (!context) return null;

    // Context expires after 30 minutes
    if (Date.now() - context.timestamp > 30 * 60 * 1000) {
      this.conversationContext.delete(key);
      return null;
    }

    return context;
  }

  async requestOpenAI(request: RequestChatbotRequestDto) {
    try {
      const { context, userId } = request;

      const suggestionAnswer = await this.getSuggestionAnswer(context);
      if (suggestionAnswer) {
        return new ResponseBuilder({ message: suggestionAnswer })
          .withCode(ResponseCodeEnum.SUCCESS)
          .withMessage(this.i18n.translate('message.SUCCESS'))
          .build();
      }

      const prevContext = this.getContext(userId);

      let queryType = this.analyzeQueryType(context);
      let databaseData;

      // Handle follow-up questions
      if (prevContext && this.isAffirmativeResponse(context)) {
        if (prevContext.suggestedProducts) {
          databaseData = {
            products: prevContext.suggestedProducts,
          };
          queryType = 'product';
        }
      } else {
        databaseData = await this.fetchDataFromDatabase(queryType, context);

        // Store suggested products for follow-up
        if (queryType === 'price' || queryType === 'product') {
          this.updateContext(userId, context, databaseData);
        }
      }

      const payload = {
        model: this.openAIModel,
        messages: [
          {
            role: 'system',
            content: this.buildPrompt(prevContext, databaseData),
          },
          {
            role: 'user',
            content: `Cho câu hỏi của người dùng "${context}"`,
          },
        ],
      };

      const response = await axios.post(this.apiUrlOpenAI, payload, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.apiKeyOpenAI}`,
        },
      });

      const aiMessage = response.data.choices[0].message.content;

      return new ResponseBuilder({ message: aiMessage })
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(this.i18n.translate('message.SUCCESS'))
        .build();
    } catch (error) {
      this.logger.error(`Chatbot API error: ${error}`);

      if (axios.isAxiosError(error)) {
        throw new BusinessException(
          `API Error: ${error.response?.data?.error?.message || error.message}`,
          ResponseCodeEnum.BAD_REQUEST,
        );
      }

      throw new BusinessException(
        this.i18n.translate('message.INTERNAL_SERVER_ERROR'),
        ResponseCodeEnum.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async requestChatbot(request: RequestChatbotRequestDto) {
    try {
      const { context, userId } = request;

      const suggestionAnswer = await this.getSuggestionAnswer(context);
      if (suggestionAnswer) {
        return new ResponseBuilder({ message: suggestionAnswer })
          .withCode(ResponseCodeEnum.SUCCESS)
          .withMessage(this.i18n.translate('message.SUCCESS'))
          .build();
      }

      const prevContext = this.getContext(userId);

      let queryType = this.analyzeQueryType(context);
      let databaseData;

      // Handle follow-up questions
      if (prevContext && this.isAffirmativeResponse(context)) {
        if (prevContext.suggestedProducts) {
          databaseData = {
            products: prevContext.suggestedProducts,
          };
          queryType = 'product';
        }
      } else {
        databaseData = await this.fetchDataFromDatabase(queryType, context);

        // Store suggested products for follow-up
        if (queryType === 'price' || queryType === 'product') {
          this.updateContext(userId, context, databaseData);
        }
      }

      const systemPrompt = `
      Bạn là trợ lý AI cho trang web bán hàng. Hãy tuân theo các quy tắc sau:
      
      1. Bạn chỉ cung cấp thông tin về sản phẩm, danh mục, giá cả dựa trên dữ liệu được cung cấp.
      2. Nếu không có thông tin về sản phẩm nào đó, thông báo cho khách hàng biết và đề xuất sản phẩm tương tự.
      3. Luôn trả lời lịch sự, ngắn gọn và hữu ích.
      4. Đề xuất các sản phẩm liên quan khi thích hợp.
      5. Không tự tạo thông tin về sản phẩm không có trong dữ liệu.
      6. Không đề cập đến việc bạn là AI hay các giới hạn của mình.
      7. Trả lời bằng tiếng Việt.
      8. Giá sản phẩm luôn hiển thị theo định dạng VND với dấu phân cách.
      9. Nếu khách hàng hỏi về chính sách đổi trả, giao hàng, thanh toán, hãy cung cấp thông tin theo chính sách chuẩn của cửa hàng.
      10. Có thêm emoji vào cuối mỗi câu trả lời cho thân thiện hơn nhé lấy từ (🌟⭐🫶☘️😉🤗☺️😚💐🫰✨🎉)

      ${prevContext ? 'Đây là câu trả lời cho câu hỏi tiếp theo của người dùng về sản phẩm đã đề xuất trước đó.' : ''}
      
      Dữ liệu sản phẩm/danh mục từ database:
      ${JSON.stringify(databaseData)}
      `;

      // Prepare API request data
      const payload = {
        contents: [
          {
            parts: [
              {
                text: `${systemPrompt} cho câu hỏi của người dùng "${context}"`,
              },
            ],
            role: 'user',
          },
        ],
        generationConfig: {
          temperature: 0.3, // Giá trị thấp để đảm bảo trả lời chính xác dựa trên dữ liệu
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1000,
        },
      };

      // Make API call to Gemini
      const config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `${this.apiUrlGemini}?key=${this.apiKeyGemini}`,
        headers: {
          'Content-Type': 'application/json',
        },
        data: JSON.stringify(payload),
      };

      const apiResponse = await axios.request(config);

      // Extract the response message from the API response
      const aiMessage =
        apiResponse.data.candidates?.[0]?.content?.parts?.[0]?.text ||
        'Xin lỗi, hiện tại tôi không thể trả lời câu hỏi của bạn. Vui lòng thử lại sau.';

      // Create response DTO
      const response = plainToInstance(
        ResponseChatbotResponseDto,
        { message: aiMessage },
        { excludeExtraneousValues: true },
      );

      return new ResponseBuilder(response)
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(this.i18n.translate('message.SUCCESS'))
        .build();
    } catch (error) {
      this.logger.error(`Chatbot API error: ${error}`);

      // Handle different types of errors
      if (axios.isAxiosError(error)) {
        throw new BusinessException(
          `API Error: ${error.response?.data?.error?.message || error.message}`,
          ResponseCodeEnum.BAD_REQUEST,
        );
      }

      throw new BusinessException(
        this.i18n.translate('message.INTERNAL_SERVER_ERROR'),
        ResponseCodeEnum.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Phân tích loại truy vấn từ người dùng
   */
  private analyzeQueryType(
    context: string,
  ): 'product' | 'category' | 'price' | 'general' {
    const lowerContext = context.toLowerCase();

    // Phân tích ngữ cảnh để xác định loại truy vấn
    if (
      lowerContext.includes('giá') ||
      lowerContext.includes('bao nhiêu tiền') ||
      lowerContext.includes('đồng')
    ) {
      return 'price';
    }

    if (
      lowerContext.includes('danh mục') ||
      lowerContext.includes('thể loại') ||
      lowerContext.includes('loại sản phẩm')
    ) {
      return 'category';
    }

    if (
      lowerContext.includes('sản phẩm') ||
      lowerContext.includes('hàng hóa') ||
      lowerContext.includes('thông tin')
    ) {
      return 'product';
    }

    return 'general';
  }

  /**
   * Lấy dữ liệu từ database dựa trên loại truy vấn
   */
  private async fetchDataFromDatabase(
    queryType: 'product' | 'category' | 'price' | 'general',
    context: string,
  ) {
    const keywords = this.extractKeywords(context);

    switch (queryType) {
      case 'product':
        // Tìm kiếm sản phẩm dựa trên từ khóa
        return await this.productRepository.findByKeywords(keywords);

      case 'category':
        // Lấy các danh mục và sản phẩm trong danh mục
        const categories =
          await this.categoryRepository.findByKeywords(keywords);
        if (categories && categories.length > 0) {
          // Lấy sản phẩm trong các danh mục này
          const productsInCategories =
            await this.productRepository.findByKeywords(keywords);
          return {
            categories,
            products: productsInCategories,
          };
        }
        return { categories: [] };

      case 'price':
        // Tìm kiếm sản phẩm và lấy thông tin giá
        return await this.productRepository.findPriceInfoByKeywords(keywords);

      case 'general':
      default:
        // Lấy các sản phẩm nổi bật, danh mục phổ biến
        const featuredProducts = await this.productRepository.findFeatured();
        const popularCategories = await this.categoryRepository.findPopular();

        return {
          featuredProducts,
          popularCategories,
          storeInfo: {
            returnPolicy: 'Đổi trả trong vòng 30 ngày',
            shippingInfo: 'Giao hàng miễn phí cho đơn hàng trên 300,000 VND',
            paymentMethods: ['Thẻ tín dụng', 'Chuyển khoản', 'COD'],
          },
        };
    }
  }

  /**
   * Trích xuất từ khóa từ câu truy vấn của người dùng
   */
  private extractKeywords(context: string): string[] {
    // Mở rộng danh sách stopwords
    const stopWords = [
      'tôi',
      'muốn',
      'tìm',
      'kiếm',
      'về',
      'cho',
      'biết',
      'có',
      'không',
      'là',
      'các',
      'những',
      'bên',
      'mình',
      'vậy',
      'ạ',
      'nhé',
      'này',
      'bao',
      'nhiêu',
      'giá',
      'món',
      'mấy',
      'thế',
      'đó',
      'được',
      'thông',
      'tin',
      'cho',
      'tôi',
      'sản',
      'trang',
      'của',
      'phẩm',
      'được',
      'bán',
      'chạy',
      'nhất',
      'danh',
      'mục',
    ];

    // Chuyển câu thành chữ thường và tách thành các từ
    const words = context.toLowerCase().split(/\s+/);

    // Lọc ra các từ khóa có ý nghĩa
    return words
      .filter(
        (word) =>
          !stopWords.includes(word) &&
          word.length >= 2 &&
          !/^[0-9]+$/.test(word), // Loại bỏ các số
      )
      .map((word) => word.replace(/[,.?!;:]/g, ''))
      .slice(0, 8); // Chỉ lấy tối đa 8 từ khóa quan trọng nhất
  }

  private isAffirmativeResponse(context: string): boolean {
    const affirmativeWords = [
      'có',
      'ừ',
      'vâng',
      'đúng',
      'ok',
      'được',
      'yes',
      'yeah',
      'muốn',
      'xem',
      'cho',
      'đồng ý',
    ];

    const normalizedContext = context.toLowerCase().trim();
    return affirmativeWords.some((word) => normalizedContext.includes(word));
  }

  async resetState(request: BaseDto): Promise<any> {
    const { userId } = request;

    const key = this.getConversationKey(userId);
    this.conversationContext.delete(key);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  private async getSuggestionAnswer(context: string): Promise<string> {
    const icons = [
      '🫰',
      '🫶',
      '🌟',
      '⭐',
      '☘️',
      '😉',
      '🤗',
      '☺️',
      '😚',
      '💐',
      '🫰',
      '✨',
      '🎉',
      '👍',
      '👏',
      '👋',
      '👐',
      '👑',
      '👒',
    ];
    const suggestionAnswersMap = {
      'thời gian giao hàng trung bình là bao lâu?':
        'Thời gian giao hàng trung bình là 5-15 phút',
      'làm sao để theo dõi đơn hàng của tôi?':
        'Bạn có thể theo dõi đơn hàng của mình qua email sau khi đặt hàng',
      'có chính sách hoàn tiền không?':
        'Có, chúng tôi có chính sách hoàn tiền nếu đơn hàng đã thành toán bị huỷ hoặc từ chối',
      'các phương thức thanh toán là gì?':
        'Chúng tôi hỗ trợ thanh toán qua thẻ tín dụng, chuyển khoản QR, ví điện tử HaUI Food hoặc thành toán khi nhận hàng',
      'làm thế nào để đặt món ăn?':
        'Bạn có thể đặt món ăn bằng cách chọn sản phẩm yêu thích và thêm vào giỏ hàng. Sau đó, bạn có thể tiến hành thanh toán đơn hàng',
    };

    const normalizedContext = context.toLowerCase().trim();
    const answer = suggestionAnswersMap[normalizedContext];

    if (answer) {
      await promiseHelper.delayRandom(150, 400);
      const icon = icons[Math.floor(Math.random() * icons.length)];
      return `${answer} ${icon}`;
    }

    return null;
  }
}
