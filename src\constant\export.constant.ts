export const EXCEL_STYLE = {
  SHEET_FONT: {
    size: 13,
    color: { argb: '000000' },
    bold: false,
    name: 'Times New Roman',
  },
  TITLE_FONT: {
    size: 13,
    bold: true,
    name: 'Times New Roman',
  },
  DEFAULT_FONT: {
    size: 12,
    bold: false,
    name: 'Times New Roman',
  },
  ALIGN_CENTER: {
    vertical: 'middle',
    horizontal: 'center',
    wrapText: true,
  },
  ALIGN_LEFT_MIDDLE: {
    vertical: 'middle',
    horizontal: 'left',
    wrapText: true,
  },
  BORDER_ALL: {
    top: { style: 'thin' },
    left: { style: 'thin' },
    right: { style: 'thin' },
    bottom: { style: 'thin' },
  },
};

export const SHEET = {
  NAME: 'Sheet',
  START_SHEET: 1,
};

export const ROW = {
  COUNT_START_ROW: 1,
  COUNT_END_ROW: 2001,
  LIMIT_EXPORT_ON_SHEET: 2000,
};

export const MAX_NUMBER_PAGE = 10;

export const DATE_FORMAT_EXPORT = 'DD/MM/YYYY';

export const DATE_FORMAT_VN = 'DD/MM/YYYY HH:mm:ss';
