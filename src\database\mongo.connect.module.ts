import { Global, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { schemas } from './schemas';
import MongoConnectService from './mongo.connect.service';
import { UserRepository } from './repository/user/user.repository';
import { CartRepository } from './repository/cart/cart.repository';
import { OrderRepository } from './repository/order/order.repository';
import { FileRepository } from '@database/repository/file/file.repository';
import { ChatRepository } from '@database/repository/chat/chat.repository';
import { ProductRepository } from './repository/product/product.repository';
import { AddressRepository } from './repository/address/address.repository';
import { LastChatRepository } from './repository/last-chat/chat.repository';
import { ContactRepository } from './repository/contact/contact.repository';
import { CategoryRepository } from './repository/category/category.repository';
import { TransactionRepository } from '@database/repository/transaction/transaction.repository';
import { OrderSummaryRepository } from '@database/repository/order-summary/order-summary.repository';
import { SystemSettingRepository } from '@database/repository/system-setting/system-setting.repository';

@Global()
@Module({
  imports: [
    MongooseModule.forRootAsync({ useClass: MongoConnectService }),
    MongooseModule.forFeature(schemas),
  ],
  providers: [
    {
      provide: 'FileRepositoryInterface',
      useClass: FileRepository,
    },
    {
      provide: 'UserRepositoryInterface',
      useClass: UserRepository,
    },
    {
      provide: 'CartRepositoryInterface',
      useClass: CartRepository,
    },
    {
      provide: 'ChatRepositoryInterface',
      useClass: ChatRepository,
    },
    {
      provide: 'OrderRepositoryInterface',
      useClass: OrderRepository,
    },
    {
      provide: 'ContactRepositoryInterface',
      useClass: ContactRepository,
    },
    {
      provide: 'ProductRepositoryInterface',
      useClass: ProductRepository,
    },
    {
      provide: 'AddressRepositoryInterface',
      useClass: AddressRepository,
    },
    {
      provide: 'CategoryRepositoryInterface',
      useClass: CategoryRepository,
    },
    {
      provide: 'LastChatRepositoryInterface',
      useClass: LastChatRepository,
    },
    {
      provide: 'TransactionRepositoryInterface',
      useClass: TransactionRepository,
    },
    {
      provide: 'OrderSummaryRepositoryInterface',
      useClass: OrderSummaryRepository,
    },
    {
      provide: 'SystemSettingRepositoryInterface',
      useClass: SystemSettingRepository,
    },
  ],
  exports: [
    {
      provide: 'FileRepositoryInterface',
      useClass: FileRepository,
    },
    {
      provide: 'UserRepositoryInterface',
      useClass: UserRepository,
    },
    {
      provide: 'CartRepositoryInterface',
      useClass: CartRepository,
    },
    {
      provide: 'ChatRepositoryInterface',
      useClass: ChatRepository,
    },
    {
      provide: 'OrderRepositoryInterface',
      useClass: OrderRepository,
    },
    {
      provide: 'ContactRepositoryInterface',
      useClass: ContactRepository,
    },
    {
      provide: 'AddressRepositoryInterface',
      useClass: AddressRepository,
    },
    {
      provide: 'ProductRepositoryInterface',
      useClass: ProductRepository,
    },
    {
      provide: 'CategoryRepositoryInterface',
      useClass: CategoryRepository,
    },
    {
      provide: 'LastChatRepositoryInterface',
      useClass: LastChatRepository,
    },
    {
      provide: 'TransactionRepositoryInterface',
      useClass: TransactionRepository,
    },
    {
      provide: 'OrderSummaryRepositoryInterface',
      useClass: OrderSummaryRepository,
    },
    {
      provide: 'SystemSettingRepositoryInterface',
      useClass: SystemSettingRepository,
    },
  ],
})
export class MongoConnectModule {}
