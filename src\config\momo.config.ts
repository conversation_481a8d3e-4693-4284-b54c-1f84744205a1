import { registerAs } from '@nestjs/config';
import { IsString, IsOptional } from 'class-validator';

import { MomoConfig } from './config.type';
import validateConfig from '@utils/validate-config';

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  MOMO_ACCESS_KEY: string;

  @IsString()
  @IsOptional()
  MOMO_API_URL: string;

  @IsString()
  @IsOptional()
  MOMO_IPN_URL: string;

  @IsString()
  @IsOptional()
  MOMO_PARTNER_CODE: string;

  @IsString()
  @IsOptional()
  MOMO_PARTNER_NAME: string;

  @IsString()
  @IsOptional()
  MOMO_REDIRECT_URL: string;

  @IsString()
  @IsOptional()
  MOMO_SECRET_KEY: string;

  @IsString()
  @IsOptional()
  MOMO_STORE_ID: string;
}

export default registerAs<MomoConfig>('momo', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  return {
    accessKey: process.env.MOMO_ACCESS_KEY || '',
    apiUrl: process.env.MOMO_API_URL || '',
    ipnUrl: process.env.MOMO_IPN_URL || '',
    partnerCode: process.env.MOMO_PARTNER_CODE || '',
    partnerName: process.env.MOMO_PARTNER_NAME || '',
    redirectUrl: process.env.MOMO_REDIRECT_URL || '',
    secretKey: process.env.MOMO_SECRET_KEY || '',
    storeId: process.env.MOMO_STORE_ID || '',
  };
});
