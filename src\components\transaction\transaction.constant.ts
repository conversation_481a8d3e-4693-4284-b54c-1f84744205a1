export const TRANSACTION_CONST = {
  MAX_DEPOSIT_REQUESTS: 15,
  MIN_DEPOSIT_AMOUNT: 10000,
  MAX_DEPOSIT_AMOUNT: 50_000_000,
  MAX_ACCOUNT_AMOUNT_BALANCE: 1_250_000_000,
  DEPOSIT_RATE_LIMIT_MINUTES: 15, // 15 minutes
  DEPOSIT_DATA_KEY_PREFIX: 'deposit_',
  DEPOSIT_COUNT_KEY_PREFIX: 'deposit_count_',
  VERIFY_TRANSACTION_TOKEN_EXPIRED_TIME: 20_000, // 20s
};

export enum TRANSACTION_ACTION_ENUM {
  DEPOSIT,
  REFUND,
  PAYMENT,
  BONUS,
}

export enum PAYMENT_TYPE_ENUM {
  MOMO = 2,
  VNPAY = 0,
  ZALOPAY = 1,
}

export enum PAYMENT_ENDPOINT_ENUM {
  MOMO = 'momo',
  VNPAY = 'vnpay',
  ZALOPAY = 'zalopay',
}
