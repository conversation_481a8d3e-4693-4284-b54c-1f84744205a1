import { isEmpty } from 'lodash';
import { Connection } from 'mongoose';
import { I18nService } from 'nestjs-i18n';
import { OnEvent } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import { InjectConnection } from '@nestjs/mongoose';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cache, CACHE_MANAGER } from '@nestjs/cache-manager';

import { BOOLEAN_ENUM } from '@constant/app.enum';
import { EVENT_ENUM } from '@constant/event.enum';
import { User } from '@database/schemas/user.model';
import { formatCurrency, generateRandomString } from '@helpers/string.helper';
import { ResponseBuilder } from '@utils/response-builder';
import {
  SYSTEM_SETTING_KEYS,
  SYSTEM_SETTING_DEFAULT_VALUES,
  KEY_ENABLE_CLOUDFLARE_CAPTCHA,
  TIME_CACHE_ENABLE_CLOUDFLARE_CAPTCHA,
} from '@components/system-setting/system-setting.constant';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { TRANSACTION_ACTION_ENUM } from '@components/transaction/transaction.constant';
import { UserRepositoryInterface } from '@database/repository/user/user.repository.interface';
import { SystemSettingRepository } from '@database/repository/system-setting/system-setting.repository';
import { TransactionRepositoryInterface } from '@database/repository/transaction/transaction.repository.interface';
import { UpdateSystemSettingRequestDto } from '@components/system-setting/dto/request/update-system-setting.request.dto';
import { DetailSystemSettingResponseDto } from '@components/system-setting/dto/response/detail-system-setting.response.dto';

@Injectable()
export class SystemSettingService {
  private readonly logger = new Logger(SystemSettingService.name);

  constructor(
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,

    private readonly i18n: I18nService,

    @InjectConnection()
    private readonly connection: Connection,

    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,

    @Inject('SystemSettingRepositoryInterface')
    private readonly systemSettingRepository: SystemSettingRepository,

    @Inject('TransactionRepositoryInterface')
    private readonly transactionRepository: TransactionRepositoryInterface,
  ) {
    this.initDefaultSettings();
  }

  private async initDefaultSettings(): Promise<void> {
    try {
      const registrationBonus = await this.systemSettingRepository.findByKey(
        SYSTEM_SETTING_KEYS.REGISTRATION_BONUS,
      );

      if (!registrationBonus) {
        await this.systemSettingRepository.setValueByKey(
          SYSTEM_SETTING_KEYS.REGISTRATION_BONUS,
          {
            enabled: BOOLEAN_ENUM.TRUE,
            amount: SYSTEM_SETTING_DEFAULT_VALUES.REGISTRATION_BONUS,
          },
          'Số tiền thưởng khi người dùng đăng ký tài khoản mới',
        );
      }

      const captchaSetting = await this.systemSettingRepository.findByKey(
        SYSTEM_SETTING_KEYS.ENABLE_CLOUDFLARE_CAPTCHA,
      );

      if (!captchaSetting) {
        await this.systemSettingRepository.setValueByKey(
          SYSTEM_SETTING_KEYS.ENABLE_CLOUDFLARE_CAPTCHA,
          {
            enabled: BOOLEAN_ENUM.FALSE,
          },
          'Bật/tắt Cloudflare Captcha khi đăng ký và đăng nhập',
        );
      }
    } catch (error) {
      this.logger.error(
        `Error initializing default settings: ${error.message}`,
      );
    }
  }

  async getSettingByKey(key: string): Promise<any> {
    return await this.systemSettingRepository.getValueByKey(key);
  }

  async updateSetting(
    key: string,
    value: any,
    description?: string,
    userId?: string,
  ): Promise<any> {
    try {
      await this.systemSettingRepository.setValueByKey(
        key,
        value,
        description,
        userId,
      );

      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(this.i18n.translate('message.UPDATE_SUCCESS'))
        .build();
    } catch (error) {
      this.logger.error(`Error updating setting ${key}: ${error.message}`);
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.INTERNAL_SERVER_ERROR)
        .withMessage(this.i18n.translate('error.INTERNAL_SERVER_ERROR'))
        .build();
    }
  }

  async getRegistrationBonus(): Promise<number> {
    const bonus = await this.getSettingByKey(
      SYSTEM_SETTING_KEYS.REGISTRATION_BONUS,
    );
    return bonus || 0;
  }

  async updateRegistrationBonus(
    amount: number,
    enabled: BOOLEAN_ENUM,
    userId?: string,
  ): Promise<any> {
    return this.updateSetting(
      SYSTEM_SETTING_KEYS.REGISTRATION_BONUS,
      {
        amount,
        enabled,
      },
      'Số tiền thưởng khi người dùng đăng ký tài khoản mới',
      userId,
    );
  }

  async isCloudflareCaptchaEnabled(): Promise<boolean> {
    const enabled = await this.getSettingByKey(
      SYSTEM_SETTING_KEYS.ENABLE_CLOUDFLARE_CAPTCHA,
    );
    return !!enabled;
  }

  async updateCloudflareCaptchaStatus(
    enabled: boolean,
    userId?: string,
  ): Promise<any> {
    return this.updateSetting(
      SYSTEM_SETTING_KEYS.ENABLE_CLOUDFLARE_CAPTCHA,
      enabled,
      'Bật/tắt Cloudflare Captcha khi đăng ký và đăng nhập',
      userId,
    );
  }

  async getAllSettings(): Promise<any> {
    try {
      const settings = await this.systemSettingRepository.find({}).lean();

      const response = plainToInstance(
        DetailSystemSettingResponseDto,
        settings,
        {
          excludeExtraneousValues: true,
        },
      );

      return new ResponseBuilder(response)
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(this.i18n.translate('message.SUCCESS'))
        .build();
    } catch (error) {
      this.logger.error(`Error getting all settings: ${error.message}`);
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.INTERNAL_SERVER_ERROR)
        .withMessage(this.i18n.translate('error.INTERNAL_SERVER_ERROR'))
        .build();
    }
  }

  async getCloudflareCaptchaStatus(): Promise<any> {
    let enableCaptcha: BOOLEAN_ENUM = await this.cacheManager.get(
      KEY_ENABLE_CLOUDFLARE_CAPTCHA,
    );

    if (
      [BOOLEAN_ENUM.FALSE, BOOLEAN_ENUM.TRUE].includes(enableCaptcha) === false
    ) {
      const docs = await this.systemSettingRepository.findOne({
        key: SYSTEM_SETTING_KEYS.ENABLE_CLOUDFLARE_CAPTCHA,
      });

      enableCaptcha = docs?.value?.enabled ?? BOOLEAN_ENUM.FALSE;

      await this.cacheManager.set(
        KEY_ENABLE_CLOUDFLARE_CAPTCHA,
        enableCaptcha,
        TIME_CACHE_ENABLE_CLOUDFLARE_CAPTCHA,
      );
    }

    return new ResponseBuilder({ enabled: enableCaptcha })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async updateSystemSetting(
    request: UpdateSystemSettingRequestDto,
  ): Promise<any> {
    const { id, value } = request;
    const setting = await this.systemSettingRepository.findOne({
      _id: id,
    });

    if (isEmpty(setting)) {
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.NOT_FOUND)
        .withMessage(this.i18n.translate('error.NOT_FOUND'))
        .build();
    }

    await this.systemSettingRepository.updateOne(
      { _id: id },
      { $set: { value } },
    );

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.UPDATE_SUCCESS'))
      .build();
  }

  @OnEvent(EVENT_ENUM.BONUS_ACCOUNT_BALANCE)
  async addBonusWhenRegister(user: User): Promise<void> {
    const bonus = await this.systemSettingRepository.findOne({
      key: SYSTEM_SETTING_KEYS.REGISTRATION_BONUS,
    });

    if (!bonus || bonus?.value?.enabled !== BOOLEAN_ENUM.TRUE) return;

    const amount = bonus.value?.amount ?? 0;
    if (amount <= 0) return;

    const session = await this.connection.startSession();
    session.startTransaction();

    try {
      await this.transactionRepository.create(
        {
          amount,
          user: user._id,
          balanceBefore: 0,
          balanceAfter: amount,
          code: generateRandomString(6),
          action: TRANSACTION_ACTION_ENUM.BONUS,
          note: `Nhận thưởng khi đăng ký tài khoản mới ${formatCurrency(amount)} VNĐ`,
        },
        { session },
      );

      await this.userRepository.updateOne(
        { _id: user._id },
        { $inc: { accountBalance: amount } },
        { session },
      );

      await session.commitTransaction();
    } catch (error) {
      this.logger.error(
        `[ADD BONUS WHEN REGISTER ERROR]: ${error} - ${JSON.stringify(error)}`,
      );
      await session.abortTransaction();
    } finally {
      this.logger.log(`[ADD BONUS WHEN REGISTER]: ${user.email}`);
      await session.endSession();
    }
  }
}
