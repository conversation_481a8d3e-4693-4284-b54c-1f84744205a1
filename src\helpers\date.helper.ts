import * as moment from 'moment';

import { SUMMARY_TIME_TYPE } from '@components/dashboard/dashboard.constant';

enum TimeUnits {
  Seconds = 'seconds',
  Minutes = 'minutes',
  Hours = 'hours',
  Days = 'days',
  Weeks = 'weeks',
  Months = 'months',
  Years = 'years',
}

export const getTimeUnit = (unit: string): TimeUnits => {
  switch (unit) {
    case 's':
      return TimeUnits.Seconds;
    case 'm':
      return TimeUnits.Minutes;
    case 'h':
      return TimeUnits.Hours;
    case 'd':
      return TimeUnits.Days;
    case 'w':
      return TimeUnits.Weeks;
    case 'M':
      return TimeUnits.Months;
    case 'y':
      return TimeUnits.Years;
    default:
      throw new Error('Invalid time unit');
  }
};

export const addTime = (startTime: Date, time: string): Date => {
  const timeValue: number = parseInt(time, 10);
  const timeUnit: string = time.slice(-1);

  const unit: TimeUnits = getTimeUnit(timeUnit);

  return moment(startTime).add(timeValue, unit).toDate();
};

export const getStartAndEndTime = (
  type: number,
): { start: Date; end: Date } => {
  const now = moment();

  switch (type) {
    case SUMMARY_TIME_TYPE.TODAY:
      return {
        start: now.startOf('day').toDate(),
        end: now.endOf('day').toDate(),
      };
    case SUMMARY_TIME_TYPE.WEEK:
      return {
        start: now.startOf('week').toDate(),
        end: now.endOf('week').toDate(),
      };
    case SUMMARY_TIME_TYPE.MONTH:
      return {
        start: now.startOf('month').toDate(),
        end: now.endOf('month').toDate(),
      };
    case SUMMARY_TIME_TYPE.YEAR:
      return {
        start: now.startOf('year').toDate(),
        end: now.endOf('year').toDate(),
      };
    default:
      throw new Error('Invalid summary order type');
  }
};
