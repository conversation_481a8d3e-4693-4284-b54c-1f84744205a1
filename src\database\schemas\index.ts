import {
  OrderSummary,
  OrderSummarySchema,
} from '@database/schemas/order-summary.model';
import {
  SystemSetting,
  SystemSettingSchema,
} from '@database/schemas/system-setting.model';
import { Cart, CartSchema } from './cart.model';
import { Chat, ChatSchema } from './chat.model';
import { File, FileSchema } from './file.model';
import { User, UserSchema } from './user.model';
import { Order, OrderSchema } from './order.model';
import { Product, ProductSchema } from './product.model';
import { Address, AddressSchema } from './address.model';
import { Contact, ContactSchema } from './contact.model';
import { Category, CategorySchema } from './category.model';
import { LastChat, LastChatSchema } from './last-chat.model';
import { Transaction, TransactionSchema } from './transaction.model';

export const schemas = [
  {
    name: User.name,
    schema: UserSchema,
  },
  {
    name: File.name,
    schema: FileSchema,
  },
  {
    name: Cart.name,
    schema: CartSchema,
  },
  {
    name: Chat.name,
    schema: ChatSchema,
  },
  {
    name: Order.name,
    schema: OrderSchema,
  },
  {
    name: Contact.name,
    schema: ContactSchema,
  },
  {
    name: Address.name,
    schema: AddressSchema,
  },
  {
    name: Product.name,
    schema: ProductSchema,
  },
  {
    name: Category.name,
    schema: CategorySchema,
  },
  {
    name: LastChat.name,
    schema: LastChatSchema,
  },
  {
    name: Transaction.name,
    schema: TransactionSchema,
  },
  {
    name: OrderSummary.name,
    schema: OrderSummarySchema,
  },
  {
    name: SystemSetting.name,
    schema: SystemSettingSchema,
  },
];
