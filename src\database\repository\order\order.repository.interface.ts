import { BaseDto } from '@core/dto/base.request.dto';
import { Order } from '@database/schemas/order.model';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { BaseInterfaceRepository } from '@core/repository/base.interface.repository';
import { GetListOrderRequestDto } from '@components/order/dto/request/get-list-order.request.dto';
import { GetSummaryOrderRequestDto } from '@components/dashboard/dto/request/get-summary-order.request.dto';

export interface OrderRepositoryInterface
  extends BaseInterfaceRepository<Order> {
  list(
    request: GetListOrderRequestDto,
    role?: USER_ROLE_ENUM,
  ): Promise<{ data: Order[]; total: number }>;

  statisticByStatus(request: BaseDto);

  getSummaryOrders(request: GetSummaryOrderRequestDto): Promise<{
    summary: { status: number; count: number }[];
    start: Date;
    end: Date;
  }>;
}
