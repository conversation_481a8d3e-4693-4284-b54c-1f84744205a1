import { isEmpty } from 'lodash';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Controller, Get, Query, UseGuards } from '@nestjs/common';

import { RoleGuard } from '@core/guards/role.guard';
import { Roles } from '@core/decorators/roles.decorator';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { DashboardService } from '@components/dashboard/dashboard.service';
import { GetSummaryOrderRequestDto } from '@components/dashboard/dto/request/get-summary-order.request.dto';
import { GetSummaryRevenueRequestDto } from '@components/dashboard/dto/request/get-summary-revenue.request.dto';
import { GetTopSellingProductRequestDto } from '@components/dashboard/dto/request/get-top-selling-product.request.dto';

@Controller('dashboards')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Get('/summary-users')
  @ApiOperation({
    summary: 'Thống kê số lượng người dùng',
    description: 'Thống kê số lượng người dùng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async getSummaryUsers() {
    return this.dashboardService.getSummaryUsers();
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Get('/summary-orders')
  @ApiOperation({
    summary: 'Thống kê số lượng đơn hàng',
    description: 'Thống kê số lượng đơn hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async getSummaryOrders(@Query() query: GetSummaryOrderRequestDto) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return this.dashboardService.getSummaryOrders(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Get('/top-5-selling-products')
  @ApiOperation({
    summary: 'Thống kê sản phẩm bán chạy nhất',
    description: 'Thống kê sản phẩm bán chạy nhất',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async getTopSellingProducts(@Query() query: GetTopSellingProductRequestDto) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return this.dashboardService.getTopSellingProducts(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Get('/summary-revenue')
  @ApiOperation({
    summary: 'Thống kê doanh thu',
    description: 'Thống kê doanh thu',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async getSummaryRevenue(@Query() query: GetSummaryRevenueRequestDto) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return this.dashboardService.getSummaryRevenue(request);
  }
}
