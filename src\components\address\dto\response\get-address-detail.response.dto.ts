import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { BaseResponseDto } from '@core/dto/base.response.dto';
import { IS_DEFAULT_ADDRESS_ENUM } from '@components/address/address.constant';

export class GetDetailAddressResponseDto extends BaseResponseDto {
  @ApiProperty()
  @Expose()
  recipientName: string;

  @ApiProperty()
  @Expose()
  phone: string;

  @ApiProperty()
  @Expose()
  address: string;

  @ApiProperty()
  @Expose()
  note: string;

  @ApiProperty()
  @Expose()
  isDefault: IS_DEFAULT_ADDRESS_ENUM;
}
