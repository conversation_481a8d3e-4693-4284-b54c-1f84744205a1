import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';

import { BaseDto } from '@core/dto/base.request.dto';
import { PAYMENT_ENDPOINT_ENUM } from '@components/transaction/transaction.constant';

export class HandleCallbackPayment extends BaseDto {
  @ApiProperty({
    enum: PAYMENT_ENDPOINT_ENUM,
    description: 'Bank',
  })
  @IsEnum(PAYMENT_ENDPOINT_ENUM)
  @IsNotEmpty()
  bank: PAYMENT_ENDPOINT_ENUM;
}
