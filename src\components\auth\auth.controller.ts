import {
  Put,
  Get,
  Res,
  Post,
  Body,
  Request,
  UseG<PERSON><PERSON>,
  Controller,
} from '@nestjs/common';
import { isEmpty } from 'lodash';
import { AuthGuard } from '@nestjs/passport';
import { Throttle } from '@nestjs/throttler';
import { ConfigService } from '@nestjs/config';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { AuthService } from './auth.service';
import { AllConfigType } from '@config/config.type';
import { BaseDto } from '@core/dto/base.request.dto';
import { Public } from '@core/decorators/public.decorator';
import { UpdateMeRequestDto } from './dto/request/update-me.request.dto';
import { LoginUserRequestDto } from './dto/request/login-user.request.dto';
import { RegisterUserRequestDto } from './dto/request/register-user.request.dto';
import { ChangePasswordRequestDto } from './dto/request/change-password.request.dto';
import { VerifyLogin2FARequestDto } from './dto/request/verify-login-2fa.request.dto';
import { Change2FASecretRequestDto } from './dto/request/change-2fa-secret-request.dto';
import { Toggle2FARequestDto } from '@components/auth/dto/request/toggle-2fa.request.dto';
import { RefreshTokenRequestDto } from '@components/auth/dto/request/refresh-token.request.dto';
import { ForgotPasswordRequestDto } from '@components/auth/dto/request/forgot-password.request.dto';
import { LoginGoogleCallbackRequestDto } from '@components/auth/dto/request/login-google-callback.request.dto';

@ApiBearerAuth()
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,

    private readonly configService: ConfigService<AllConfigType>,
  ) {}

  @Public()
  @Post('/register')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Đăng ký mới người dùng',
    description: 'Đăng ký mới người dùng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async register(@Body() payload: RegisterUserRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.authService.register(request);
  }

  @Public()
  @Post('/login')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Đăng nhập hệ thống',
    description: 'Đăng nhập hệ thống',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async login(@Body() payload: LoginUserRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.authService.login(request);
  }

  @Public()
  @Post('/verify-login-2fa')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Xác thực đăng nhập với 2FA',
    description: 'Xác thực đăng nhập với 2FA',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async verifyLogin2FA(@Body() payload: VerifyLogin2FARequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.authService.verifyLogin2FA(request);
  }

  @Throttle({
    default: {
      limit: 300,
      ttl: 50_000,
    },
  }) // 300 requests per 50 seconds
  @Get('/me')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Thông tin cá nhân',
    description: 'Thông tin cá nhân',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  getMe(@Request() request: BaseDto) {
    return this.authService.getMe(request);
  }

  @Put('/me')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Cập nhật thông tin cá nhân',
    description: 'Cập nhật thông tin cá nhân',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async updateMe(@Body() payload: UpdateMeRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.authService.updateMe(request);
  }

  @Put('/change-password')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Thay đổi mật khẩu',
    description: 'Thay đổi mật khẩu',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async changePassword(@Body() payload: ChangePasswordRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.authService.changePassword(request);
  }

  @Post('/generate-secret-key')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Tạo khoá 2FA mới',
    description: 'Tạo khoá 2FA mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async generateSecretKey() {
    return await this.authService.generateSecretKey();
  }

  @Post('/change-2fa-secret')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Đổi khoá 2FA',
    description: 'Đổi khoá 2FA',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async change2FASecret(@Body() payload: Change2FASecretRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.authService.change2FASecret(request);
  }

  @Put('/toggle-2fa')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Đổi khoá 2FA',
    description: 'Đổi khoá 2FA',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async toggle2FA(@Body() payload: Toggle2FARequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.authService.toggle2FA(request);
  }

  @Public()
  @Post('/forgot-password')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Forgot password',
    description: 'Forgot password',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async forgotPassword(@Body() payload: ForgotPasswordRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.authService.forgotPassword(request);
  }

  @Public()
  @Post('/refresh-tokens')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Forgot password',
    description: 'Forgot password',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async refreshTokens(@Body() payload: RefreshTokenRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.authService.refreshToken(request);
  }

  @Public()
  @Get('google')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Google login',
    description: 'Google login',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  @UseGuards(AuthGuard('google'))
  googleAuth() {}

  @Public()
  @Get('google/callback')
  @ApiOperation({
    tags: ['Auth'],
    summary: 'Google Callback',
    description: 'Google Callback',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  @UseGuards(AuthGuard('google'))
  async googleAuthRedirect(
    @Request() request: LoginGoogleCallbackRequestDto,
    @Res() response,
  ) {
    const result = await this.authService.loginWithGoogle(request);
    const appConfig = this.configService.getOrThrow('app', { infer: true });
    const frontendUrl = appConfig.frontendDomain || 'http://localhost:3000';

    const accessToken = encodeURIComponent(
      JSON.stringify(result?.data?.accessToken),
    );
    const refreshToken = encodeURIComponent(
      JSON.stringify(result?.data?.refreshToken),
    );
    const user = encodeURIComponent(JSON.stringify(result?.data?.user));

    return response.redirect(
      `${frontendUrl}/auth/callback?accessToken=${accessToken}&refreshToken=${refreshToken}&user=${user}`,
    );
  }
}
