import { Expose } from 'class-transformer';

export class PaymentZaloPayResponseDto {
  @Expose({ name: 'return_code' })
  returnCode: string;

  @Expose({ name: 'return_message' })
  returnMessage: string;

  @Expose({ name: 'sub_return_code' })
  subReturnCode: string;

  @Expose({ name: 'sub_return_message' })
  subReturnMessage: string;

  @Expose({ name: 'zp_trans_token' })
  zpTransToken: string;

  @Expose({ name: 'order_url' })
  orderUrl: string;

  @Expose({ name: 'order_token' })
  orderToken: string;
}
