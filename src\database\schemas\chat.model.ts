import * as mongoose from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BOOLEAN_ENUM } from '@constant/app.enum';
import { BaseModel } from '@core/schema/base.model';

@Schema({
  timestamps: true,
  collection: 'chats',
  collation: { locale: 'vi' },
})
export class Chat extends BaseModel {
  @Prop({
    type: String,
    required: true,
  })
  message: string;

  @Prop({
    type: String,
    required: false,
  })
  sender: string;

  @Prop({
    type: String,
    required: false,
  })
  receiver: string;

  @Prop({
    type: Number,
    enum: BOOLEAN_ENUM,
    default: BOOLEAN_ENUM.TRUE,
  })
  isUserMsg: number;
}

export const ChatSchema = SchemaFactory.createForClass(Chat);

export type ChatDocument = Chat & mongoose.Document;
