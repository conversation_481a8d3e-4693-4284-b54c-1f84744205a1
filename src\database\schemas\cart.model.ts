import * as mongoose from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseModel } from '@core/schema/base.model';

@Schema({
  timestamps: true,
  collation: { locale: 'vi' },
})
export class CartDetail extends BaseModel {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true,
  })
  product: string;

  @Prop({
    type: Number,
    required: true,
    min: 1,
  })
  quantity: number;
}

export const CartDetailSchema = SchemaFactory.createForClass(CartDetail);

export type CartDetailDocument = CartDetail & mongoose.Document;

@Schema({
  timestamps: true,
  collection: 'carts',
  collation: { locale: 'vi' },
})
export class Cart extends BaseModel {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true,
    required: true,
  })
  user: string;

  @Prop({
    type: [CartDetailSchema],
    required: false,
    default: [],
  })
  cartDetails?: CartDetail[];
}

export const CartSchema = SchemaFactory.createForClass(Cart);

export type CartDocument = Cart & mongoose.Document;
