import { isEmpty } from 'lodash';
import * as bcrypt from 'bcrypt';
import * as twoFactor from 'node-2fa';
import { JwtService } from '@nestjs/jwt';
import { I18nService } from 'nestjs-i18n';
import { ConfigService } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cache, CACHE_MANAGER } from '@nestjs/cache-manager';

import {
  AUTH_CONST,
  KEY_PASSWORD_RESET,
  PASSWORD_RESET_EXPIRES,
} from './auth.constant';
import {
  IS_2FA_ENUM,
  USER_ROLE_ENUM,
  USER_LOCKED_ENUM,
  USER_GENDER_ENUM,
  SALT_ROUNDS_PASSWORD,
} from '@components/user/user.constant';
import { EVENT_ENUM } from '@constant/event.enum';
import { BOOLEAN_ENUM } from '@constant/app.enum';
import { AllConfigType } from '@config/config.type';
import { User } from '@database/schemas/user.model';
import { BaseDto } from '@core/dto/base.request.dto';
import { expiresCheck } from '@helpers/crypto.helper';
import { IJwtPayload } from '@core/guards/authen.guard';
import { ResponseBuilder } from '@utils/response-builder';
import {
  SYSTEM_SETTING_KEYS,
  KEY_ENABLE_CLOUDFLARE_CAPTCHA,
  TIME_CACHE_ENABLE_CLOUDFLARE_CAPTCHA,
} from '@components/system-setting/system-setting.constant';
import { generateRandomString } from '@helpers/string.helper';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { MAIL_TEMPLATE_ENUM } from '@components/mail/mail.constant';
import { GetMeResponseDto } from './dto/response/get-me.response.dto';
import { UpdateMeRequestDto } from './dto/request/update-me.request.dto';
import { LoginUserRequestDto } from './dto/request/login-user.request.dto';
import { CloudflareService } from '@components/cloudflare/cloudflare.service';
import { SendMailRequestDto } from '@components/mail/dto/send-mail.request.dto';
import { RegisterUserRequestDto } from './dto/request/register-user.request.dto';
import { LoginSuccessResponseDto } from './dto/response/login-success.response.dto';
import { BusinessException } from '@core/exception-filter/business-exception.filter';
import { ChangePasswordRequestDto } from './dto/request/change-password.request.dto';
import { VerifyLogin2FARequestDto } from './dto/request/verify-login-2fa.request.dto';
import { Change2FASecretRequestDto } from './dto/request/change-2fa-secret-request.dto';
import { Toggle2FARequestDto } from '@components/auth/dto/request/toggle-2fa.request.dto';
import { CreateUserRequestDto } from '@components/user/dto/request/create-user.request.dto';
import { UserRepositoryInterface } from '@database/repository/user/user.repository.interface';
import { RefreshTokenRequestDto } from '@components/auth/dto/request/refresh-token.request.dto';
import { RefreshTokenResponseDto } from '@components/auth/dto/response/refresh-token.response.dto';
import { ForgotPasswordRequestDto } from '@components/auth/dto/request/forgot-password.request.dto';
import { SystemSettingRepository } from '@database/repository/system-setting/system-setting.repository';
import { LoginGoogleCallbackRequestDto } from '@components/auth/dto/request/login-google-callback.request.dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,

    private jwtService: JwtService,

    private readonly i18n: I18nService,

    private readonly eventEmitter: EventEmitter2,

    private configService: ConfigService<AllConfigType>,

    private readonly cloudflareService: CloudflareService,

    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,

    @Inject('SystemSettingRepositoryInterface')
    private readonly systemSettingRepository: SystemSettingRepository,
  ) {}

  async register(request: RegisterUserRequestDto) {
    const { email, captchaToken } = request;

    let enableCaptcha: BOOLEAN_ENUM = await this.cacheManager.get(
      KEY_ENABLE_CLOUDFLARE_CAPTCHA,
    );

    if (
      [BOOLEAN_ENUM.FALSE, BOOLEAN_ENUM.TRUE].includes(enableCaptcha) === false
    ) {
      const docs = await this.systemSettingRepository.findOne({
        key: SYSTEM_SETTING_KEYS.ENABLE_CLOUDFLARE_CAPTCHA,
      });

      enableCaptcha = docs?.value?.enabled ?? BOOLEAN_ENUM.FALSE;

      await this.cacheManager.set(
        KEY_ENABLE_CLOUDFLARE_CAPTCHA,
        enableCaptcha,
        TIME_CACHE_ENABLE_CLOUDFLARE_CAPTCHA,
      );
    }

    if (enableCaptcha === BOOLEAN_ENUM.TRUE) {
      if (!captchaToken) {
        throw new BusinessException(
          this.i18n.translate('error.CAPTCHA_REQUIRED'),
          ResponseCodeEnum.BAD_REQUEST,
        );
      }

      const verifyResult =
        await this.cloudflareService.verifyCaptcha(captchaToken);
      if (!verifyResult?.success) {
        throw new BusinessException(
          this.i18n.translate('error.VALIDATE_CAPTCHA_FAILED'),
          ResponseCodeEnum.BAD_REQUEST,
        );
      }
    }

    const emailExist = await this.userRepository.findOne({ email });
    if (!isEmpty(emailExist)) {
      throw new BusinessException(
        this.i18n.translate('error.EMAIL_EXIST'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const { fullname, password } = request;
    const newEntity = this.userRepository.createEntity({
      email,
      fullname,
      password,
      role: USER_ROLE_ENUM.USER,
      gender: USER_GENDER_ENUM.FEMALE,
    } as CreateUserRequestDto);

    const user = await newEntity.save();

    this.eventEmitter.emit(EVENT_ENUM.INIT_CART, user);
    this.eventEmitter.emit(EVENT_ENUM.BONUS_ACCOUNT_BALANCE, user);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.CREATED)
      .withMessage(this.i18n.translate('message.REGISTER_SUCCESS'))
      .build();
  }

  async login(request: LoginUserRequestDto) {
    const { email, password, captchaToken } = request;

    let enableCaptcha: BOOLEAN_ENUM = await this.cacheManager.get(
      KEY_ENABLE_CLOUDFLARE_CAPTCHA,
    );

    if (
      [BOOLEAN_ENUM.FALSE, BOOLEAN_ENUM.TRUE].includes(enableCaptcha) === false
    ) {
      const docs = await this.systemSettingRepository.findOne({
        key: SYSTEM_SETTING_KEYS.ENABLE_CLOUDFLARE_CAPTCHA,
      });

      enableCaptcha = docs?.value?.enabled ?? BOOLEAN_ENUM.FALSE;

      await this.cacheManager.set(
        KEY_ENABLE_CLOUDFLARE_CAPTCHA,
        enableCaptcha,
        TIME_CACHE_ENABLE_CLOUDFLARE_CAPTCHA,
      );
    }

    if (enableCaptcha === BOOLEAN_ENUM.TRUE) {
      if (!captchaToken) {
        throw new BusinessException(
          this.i18n.translate('error.CAPTCHA_REQUIRED'),
          ResponseCodeEnum.BAD_REQUEST,
        );
      }

      const verifyResult =
        await this.cloudflareService.verifyCaptcha(captchaToken);
      if (!verifyResult?.success) {
        throw new BusinessException(
          this.i18n.translate('error.VALIDATE_CAPTCHA_FAILED'),
          ResponseCodeEnum.BAD_REQUEST,
        );
      }
    }

    const user = await this.userRepository.findOne({ email });
    if (isEmpty(user)) {
      throw new BusinessException(
        this.i18n.translate('error.EMAIL_OR_PASSWORD_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    if (user.isLocked === USER_LOCKED_ENUM.LOCKED) {
      throw new BusinessException(
        this.i18n.translate('error.ACCOUNT_IS_LOCKED'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const passwordReset = await this.cacheManager.get(
      `${KEY_PASSWORD_RESET}-${user._id.toString()}`,
    );

    const isPasswordResetValid =
      passwordReset && (await bcrypt.compare(password, passwordReset));
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid && !isPasswordResetValid) {
      throw new BusinessException(
        this.i18n.translate('error.EMAIL_OR_PASSWORD_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const authConfig = this.configService.get('auth', {
      infer: true,
    });

    if (user.is2FA === IS_2FA_ENUM.ENABLED) {
      const { two2FASecret, two2FAExpires: two2FAExpiresIn } = authConfig;

      const token2FA = this.jwtService.sign({ id: user.id } as IJwtPayload, {
        secret: two2FASecret,
        expiresIn: two2FAExpiresIn,
      });

      return new ResponseBuilder({
        token2FA,
      })
        .withCode(ResponseCodeEnum.ACCEPTED)
        .withMessage(this.i18n.translate('error.PLEASE_VERIFY_2FA'))
        .build();
    }

    return await this.buildDataLoginSuccess(user);
  }

  async verifyLogin2FA(request: VerifyLogin2FARequestDto) {
    const { token2FA, otp } = request;

    const tokenUsed = await this.cacheManager.get(token2FA);
    if (tokenUsed) {
      throw new BusinessException(
        this.i18n.translate('error.TOKEN_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    let payload: IJwtPayload | null = null;

    const authConfig = this.configService.get('auth', {
      infer: true,
    });

    try {
      payload = await this.jwtService.verify(token2FA, {
        secret: authConfig.two2FASecret,
      });
    } catch {
      throw new BusinessException(
        this.i18n.translate('error.TOKEN_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const { id } = payload;
    const user = await this.userRepository.findOne({ _id: id });
    if (isEmpty(user)) {
      throw new BusinessException(
        this.i18n.translate('error.TOKEN_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const isValid = this.verify2FASecret(user.twoFactorSecret, otp);
    if (!isValid) {
      throw new BusinessException(
        this.i18n.translate('error.OTP_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    await this.cacheManager.set(token2FA, true, 30_000);

    return await this.buildDataLoginSuccess(user);
  }

  async loginWithGoogle(request: LoginGoogleCallbackRequestDto) {
    const { user } = request;
    const { email, fullname, avatar } = user;

    const userExist = await this.userRepository.findOne({ email });

    // trường hợp đã tồn tại trên hệ thống
    if (!isEmpty(userExist)) {
      if (userExist.isLocked === USER_LOCKED_ENUM.LOCKED) {
        throw new BusinessException(
          this.i18n.translate('error.ACCOUNT_IS_LOCKED'),
          ResponseCodeEnum.BAD_REQUEST,
        );
      }

      // @todo implement
      return await this.buildDataLoginSuccess(userExist);
    }

    const password = generateRandomString(AUTH_CONST.PASSWORD.LENGTH_DEFAULT);

    const newEntity = this.userRepository.createEntity({
      email,
      avatar,
      fullname,
      password,
      role: USER_ROLE_ENUM.USER,
      gender: USER_GENDER_ENUM.FEMALE,
    } as CreateUserRequestDto);

    const newUser = await newEntity.save();

    this.eventEmitter.emit(EVENT_ENUM.INIT_CART, newUser);
    this.eventEmitter.emit(EVENT_ENUM.BONUS_ACCOUNT_BALANCE, newUser);

    const payloadSendEmail = {
      email,
      body: {
        template: MAIL_TEMPLATE_ENUM.REGISTER_WITH_GOOGLE,
        subject: this.i18n.translate('email.REGISTER_WITH_GOOGLE_SUBJECT'),
        context: {
          password,
        },
      },
    } as SendMailRequestDto;

    this.eventEmitter.emit(EVENT_ENUM.SEND_MAIL, payloadSendEmail);

    return await this.buildDataLoginSuccess(newUser);
  }

  getMe(request: BaseDto) {
    const response = plainToInstance(GetMeResponseDto, request.user, {
      excludeExtraneousValues: true,
    });
    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async updateMe(request: UpdateMeRequestDto) {
    const { user } = request;

    const newMe = this.userRepository.updateMe(user, request);
    await newMe.save();

    const response = plainToInstance(GetMeResponseDto, newMe, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async changePassword(request: ChangePasswordRequestDto) {
    const { user, oldPassword, newPassword } = request;

    const isMatch = await bcrypt.compare(oldPassword, user.password);
    if (!isMatch) {
      throw new BusinessException(
        this.i18n.translate('error.OLD_PASSWORD_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    user.password = newPassword;
    await user.save();

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.CHANGE_PASSWORD_SUCCESS'))
      .build();
  }

  async generateSecretKey() {
    const { secret } = twoFactor.generateSecret();
    return new ResponseBuilder({ twoFactorSecret: secret })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async change2FASecret(request: Change2FASecretRequestDto) {
    const { twoFactorSecret, otp, userId } = request;

    const isValid = this.verify2FASecret(twoFactorSecret, otp);
    if (!isValid) {
      throw new BusinessException(
        this.i18n.translate('error.OTP_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    await this.userRepository.findOneAndUpdate(
      { _id: userId },
      { twoFactorSecret },
    );

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.CHANGE_2FA_SECRET_SUCCESS'))
      .build();
  }

  async toggle2FA(request: Toggle2FARequestDto) {
    const { userId, is2FA, user, otp } = request;

    if (user.is2FA === is2FA) {
      throw new BusinessException(
        this.i18n.translate('error.TWO_FA_ALREADY_SET'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    if (is2FA === IS_2FA_ENUM.ENABLED) {
      const isValid = this.verify2FASecret(user.twoFactorSecret, otp);
      if (!isValid) {
        throw new BusinessException(
          this.i18n.translate('error.OTP_INVALID'),
          ResponseCodeEnum.BAD_REQUEST,
        );
      }
    }

    await this.userRepository.findOneAndUpdate({ _id: userId }, { is2FA });

    const messageAction =
      is2FA === IS_2FA_ENUM.ENABLED
        ? 'ENABLE_2FA_SUCCESS'
        : 'DISABLE_2FA_SUCCESS';

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate(`message.${messageAction}`))
      .build();
  }

  async refreshToken(request: RefreshTokenRequestDto) {
    const { refreshToken } = request;

    let payload: IJwtPayload | null = null;

    const authConfig = this.configService.get('auth', {
      infer: true,
    });

    const {
      accessSecret,
      accessExpires: accessTokenExpiresIn,
      refreshSecret,
      refreshExpires: refreshTokenExpiresIn,
    } = authConfig;

    try {
      payload = (await this.jwtService.verify(refreshToken, {
        secret: refreshSecret,
      })) as IJwtPayload;
    } catch (e) {
      switch (e.name) {
        case 'TokenExpiredError':
          throw new BusinessException(
            this.i18n.translate('error.TOKEN_EXPIRED'),
            ResponseCodeEnum.BAD_REQUEST,
          );
        case 'JsonWebTokenError':
          throw new BusinessException(
            this.i18n.translate('error.TOKEN_INVALID'),
            ResponseCodeEnum.BAD_REQUEST,
          );
        default:
          throw new BusinessException(
            this.i18n.translate('error.TOKEN_INVALID'),
            ResponseCodeEnum.BAD_REQUEST,
          );
      }
    }

    const { id } = payload;
    const user = await this.userRepository.findOne({ _id: id });
    if (isEmpty(user)) {
      throw new BusinessException(
        this.i18n.translate('error.TOKEN_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const [accessToken, refreshTokenNew] = await Promise.all([
      this.jwtService.sign(
        {
          id: user.id,
          email: user.email,
          role: user.role,
          isAdmin: user.role === USER_ROLE_ENUM.ADMIN,
        } as IJwtPayload,
        {
          secret: accessSecret,
          expiresIn: accessTokenExpiresIn,
        },
      ),
      this.jwtService.sign(
        {
          id: user.id,
          role: user.role,
          isAdmin: user.role === USER_ROLE_ENUM.ADMIN,
        } as IJwtPayload,
        {
          secret: refreshSecret,
          expiresIn: refreshTokenExpiresIn,
        },
      ),
    ]);

    const response = plainToInstance(
      RefreshTokenResponseDto,
      {
        accessToken,
        refreshToken: refreshTokenNew,
      },
      {
        excludeExtraneousValues: true,
      },
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async forgotPassword(request: ForgotPasswordRequestDto) {
    const { email, sign, text } = request;

    try {
      const isCorrect = await this.verifyCaptcha(sign, text);
      if (!isCorrect) {
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.BAD_REQUEST)
          .withMessage(this.i18n.translate('error.CAPTCHA_INVALID'))
          .build();
      }
    } catch (error) {
      this.logger.error(`VERIFY CAPTCHA FORGOT PASSWORD ERROR: ${error}`);
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(this.i18n.translate('error.CAPTCHA_INVALID'))
        .build();
    }

    const user = await this.userRepository.findOne({ email });
    if (isEmpty(user)) {
      throw new BusinessException(
        this.i18n.translate('error.EMAIL_NOT_EXIST'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const passwordReset = generateRandomString(
      AUTH_CONST.PASSWORD.LENGTH_DEFAULT,
    );

    this.logger.log(`PASSWORD RESET: ${passwordReset}`);

    const hashPassword = await bcrypt.hash(passwordReset, SALT_ROUNDS_PASSWORD);

    await this.cacheManager.set(
      `${KEY_PASSWORD_RESET}-${user._id.toString()}`,
      hashPassword,
      PASSWORD_RESET_EXPIRES,
    );

    const payloadSendEmail = {
      email,
      body: {
        template: MAIL_TEMPLATE_ENUM.FORGOT_PASSWORD,
        subject: this.i18n.translate('email.FORGOT_PASSWORD_SUBJECT'),
        context: {
          passwordReset,
        },
      },
    } as SendMailRequestDto;

    this.eventEmitter.emit(EVENT_ENUM.SEND_MAIL, payloadSendEmail);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.NEW_PASSWORD_SENT_TO_EMAIL'))
      .build();
  }

  private verify2FASecret(secret: string, otp: string): boolean {
    const result = twoFactor.verifyToken(secret, otp);

    if (!result) return false;

    return AUTH_CONST.CODE_VERIFY_2FA_SUCCESS.includes(result.delta);
  }

  private async buildDataLoginSuccess(user: User) {
    const authConfig = this.configService.get('auth', {
      infer: true,
    });

    const {
      accessSecret,
      accessExpires: accessTokenExpiresIn,
      refreshSecret,
      refreshExpires: refreshTokenExpiresIn,
    } = authConfig;

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.sign(
        {
          id: user.id,
          role: user.role,
          email: user.email,
          isAdmin: user.role === USER_ROLE_ENUM.ADMIN,
        } as IJwtPayload,
        {
          secret: accessSecret,
          expiresIn: accessTokenExpiresIn,
        },
      ),
      this.jwtService.sign(
        {
          id: user.id,
          role: user.role,
          isAdmin: user.role === USER_ROLE_ENUM.ADMIN,
        } as IJwtPayload,
        {
          secret: refreshSecret,
          expiresIn: refreshTokenExpiresIn,
        },
      ),
    ]);

    const response = plainToInstance(
      LoginSuccessResponseDto,
      {
        user,
        accessToken,
        refreshToken,
      },
      {
        excludeExtraneousValues: true,
      },
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.LOGIN_SUCCESS'))
      .build();
  }

  private async verifyCaptcha(sign: string, text: string): Promise<boolean> {
    const signUsed = await this.cacheManager.get(sign);

    if (signUsed) return false;

    const authConfig = this.configService.get('auth', {
      infer: true,
    });

    const { isExpired, payload } = expiresCheck(sign, authConfig.captchaSecret);

    if (text === payload?.text && !isExpired) {
      await this.cacheManager.set(sign, true, 30_000);
      return true;
    }

    return false;
  }
}
