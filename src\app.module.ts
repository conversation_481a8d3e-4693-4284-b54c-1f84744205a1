import {
  I18nModule,
  <PERSON>ry<PERSON><PERSON><PERSON><PERSON>,
  HeaderRes<PERSON>ver,
  CookieResolver,
  I18n<PERSON>son<PERSON><PERSON>der,
  AcceptLanguageResolver,
} from 'nestjs-i18n';
import Redis from 'ioredis';
import * as path from 'path';
import { JwtModule } from '@nestjs/jwt';
import { APP_GUARD, APP_PIPE } from '@nestjs/core';
import { ThrottlerModule } from '@nestjs/throttler';
import { ServeStaticModule } from '@nestjs/serve-static';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ThrottlerStorageRedisService } from '@nest-lab/throttler-storage-redis';

import appConfig from '@config/app.config';
import { AppService } from './app.service';
import authConfig from '@config/auth.config';
import momoConfig from '@config/momo.config';
import VnPayConfig from '@config/vn-pay.config';
import { AppController } from './app.controller';
import zaloPayConfig from '@config/zalo-pay.config';
import { AllConfigType } from '@config/config.type';
import databaseConfig from '@config/database.config';
import { AuthenGuard } from '@core/guards/authen.guard';
import cloudflareConfig from '@config/cloudflare.config';
import { AuthModule } from '@components/auth/auth.module';
import { UserModule } from '@components/user/user.module';
import { MailModule } from '@components/mail/mail.module';
import { FileModule } from '@components/file/file.module';
import { CronModule } from '@components/cron/cron.module';
import { CartModule } from '@components/cart/cart.module';
import { ChatModule } from '@components/chat/chat.module';
import { ValidationPipe } from '@core/pipe/validator.pipe';
import { OrderModule } from '@components/order/order.module';
import { SocketModule } from '@components/socket/socket.module';
import { ProductModule } from '@components/product/product.module';
import { AddressModule } from '@components/address/address.module';
import { ContactModule } from '@components/contact/contact.module';
import { CaptchaModule } from '@components/captcha/captcha.module';
import { ChatbotModule } from '@components/chatbot/chatbot.module';
import { MongoConnectModule } from '@database/mongo.connect.module';
import { CategoryModule } from '@components/category/category.module';
import { TelegramModule } from '@components/telegram/telegram.module';
import { DashboardModule } from '@components/dashboard/dashboard.module';
import { CustomThrottlerGuard } from '@core/guards/custom-throttler.guard';
import { CloudflareModule } from '@components/cloudflare/cloudflare.module';
import { RedisCacheModule } from '@core/components/redis/redis-cache.module';
import { TransactionModule } from '@components/transaction/transaction.module';
import { SystemSettingModule } from '@components/system-setting/system-setting.module';
import { SystemMonitorModule } from '@components/system-monitor/system-monitor.module';
import { RequestLoggingMiddleware } from '@core/middlewares/request-logging.middleware';

@Module({
  imports: [
    ServeStaticModule.forRoot({
      rootPath: path.join(__dirname, '..', 'src', 'views'),
      serveRoot: '/views',
    }),
    SocketModule,
    JwtModule.register({}),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        appConfig,
        authConfig,
        momoConfig,
        VnPayConfig,
        zaloPayConfig,
        databaseConfig,
        cloudflareConfig,
      ],
      envFilePath: ['.env'],
    }),
    I18nModule.forRootAsync({
      useFactory: (configService: ConfigService<AllConfigType>) => ({
        fallbackLanguage: configService.getOrThrow('app.fallbackLanguage', {
          infer: true,
        }),
        loader: I18nJsonLoader,
        loaderOptions: { path: path.join(__dirname, '/i18n/'), watch: true },
      }),
      resolvers: [
        new CookieResolver(),
        AcceptLanguageResolver,
        new HeaderResolver(['x-lang']),
        { use: QueryResolver, options: ['lang', 'locale', 'l'] },
      ],
      imports: [ConfigModule],
      inject: [ConfigService],
    }),
    ThrottlerModule.forRoot({
      throttlers: [
        {
          ttl: 60_000,
          limit: 1000,
        },
      ],
      storage: new ThrottlerStorageRedisService(
        new Redis({
          host: process.env.REDIS_HOST || 'localhost',
          port: Number(process.env.REDIS_PORT) || 6379,
        }),
      ),
    }),
    MongoConnectModule,
    EventEmitterModule.forRoot(),
    MailModule,
    FileModule,
    UserModule,
    AuthModule,
    CartModule,
    CronModule,
    ChatModule,
    OrderModule,
    ChatbotModule,
    CaptchaModule,
    ContactModule,
    AddressModule,
    ProductModule,
    TelegramModule,
    CategoryModule,
    DashboardModule,
    CloudflareModule,
    RedisCacheModule,
    TransactionModule,
    SystemSettingModule,
    SystemMonitorModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_PIPE,
      useClass: ValidationPipe,
    },
    {
      provide: APP_GUARD,
      useClass: AuthenGuard,
    },
    {
      provide: APP_GUARD,
      useClass: CustomThrottlerGuard,
    },
    AppService,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestLoggingMiddleware).forRoutes('*');
  }
}
