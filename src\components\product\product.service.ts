import slugify from 'slugify';
import { isEmpty, keyBy } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { OnEvent } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import { Inject, Injectable } from '@nestjs/common';

import {
  formatDateVN,
  formatNumber,
  exportOneSheetUtil,
  formatStringExport,
  createExportHeaders,
} from '@helpers/export.helper';
import { EVENT_ENUM } from '@constant/event.enum';
import { Order } from '@database/schemas/order.model';
import { PRODUCT_STATUS_ENUM } from './product.constant';
import { ResponseBuilder } from '@utils/response-builder';
import { IdParamDto } from '@core/dto/param-id.request.dto';
import { BaseResponseDto } from '@core/dto/base.response.dto';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { CreateProductRequestDto } from './dto/request/create-product.request.dto';
import { UpdateProductRequestDto } from './dto/request/update-product.request.dto';
import { BusinessException } from '@core/exception-filter/business-exception.filter';
import { GetListProductRequestDto } from './dto/request/get-list-product.request.dto';
import { GetDetailProductRequestDto } from './dto/request/get-detail-product.request.dto';
import { GetDetailProductResponseDto } from './dto/response/get-product-detail.response.dto';
import { UserRepositoryInterface } from '@database/repository/user/user.repository.interface';
import { ProductRepositoryInterface } from '@database/repository/product/product.repository.interface';

@Injectable()
export class ProductService {
  constructor(
    private readonly i18n: I18nService,

    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,

    @Inject('ProductRepositoryInterface')
    private readonly productRepository: ProductRepositoryInterface,
  ) {}
  async create(request: CreateProductRequestDto) {
    const { code, name } = request;

    const codeExist = await this.productRepository.findOne({ code });
    if (!isEmpty(codeExist)) {
      throw new BusinessException(
        this.i18n.translate('error.PRODUCT_EXIST', {
          args: { code },
        }),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const productEntity = this.productRepository.createEntity({
      ...request,
      slug: `${code}-${slugify(name, { lower: true })}`,
    });

    await productEntity.save();

    const response = plainToInstance(BaseResponseDto, productEntity, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.CREATED)
      .withMessage(this.i18n.translate('message.CREATE_SUCCESS'))
      .build();
  }

  async list(request: GetListProductRequestDto) {
    const { data, total } = await this.productRepository.list(request);

    const response = plainToInstance(GetDetailProductResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      meta: { total, page: request.page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailProductRequestDto) {
    const { id } = request;
    const product = await this.productRepository
      .findOne({
        _id: id,
        deletedAt: null,
      })
      .populate('category');

    if (isEmpty(product)) {
      throw new BusinessException(
        this.i18n.translate('error.NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const response = plainToInstance(GetDetailProductResponseDto, product, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async update(request: UpdateProductRequestDto) {
    const { id, name, code } = request;

    const product = await this.productRepository.getDetail(id);
    if (isEmpty(product)) {
      throw new BusinessException(
        this.i18n.translate('error.NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const productEntity = this.productRepository.updateEntity(product, {
      ...request,
      slug: `${code}-${slugify(name, { lower: true })}`,
    });

    await productEntity.save();

    const response = plainToInstance(
      GetDetailProductResponseDto,
      productEntity,
      {
        excludeExtraneousValues: true,
      },
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.UPDATE_SUCCESS'))
      .build();
  }

  async delete(request: IdParamDto) {
    const { id, userId } = request;

    const product = await this.productRepository.getDetail(id);
    if (isEmpty(product)) {
      throw new BusinessException(
        this.i18n.translate('error.NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    if (product.status !== PRODUCT_STATUS_ENUM.PAUSED) {
      throw new BusinessException(
        this.i18n.translate('error.PRODUCT_STATUS_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    product.deletedBy = userId;
    product.deletedAt = new Date();
    await product.save();

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.DELETE_SUCCESS'))
      .build();
  }

  async active(request: IdParamDto) {
    const { id } = request;

    const product = await this.productRepository.getDetail(id);
    if (isEmpty(product)) {
      throw new BusinessException(
        this.i18n.translate('error.NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const statusesCanActive = [
      PRODUCT_STATUS_ENUM.PAUSED,
      PRODUCT_STATUS_ENUM.UPCOMING,
    ];
    if (!statusesCanActive.includes(product.status)) {
      throw new BusinessException(
        this.i18n.translate('error.STATUS_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    product.status = PRODUCT_STATUS_ENUM.ACTIVE;
    await product.save();

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async pause(request: IdParamDto) {
    const { id } = request;

    const product = await this.productRepository.getDetail(id);
    if (isEmpty(product)) {
      throw new BusinessException(
        this.i18n.translate('error.NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const statusesCanActive = [
      PRODUCT_STATUS_ENUM.ACTIVE,
      PRODUCT_STATUS_ENUM.UPCOMING,
    ];
    if (!statusesCanActive.includes(product.status)) {
      throw new BusinessException(
        this.i18n.translate('error.STATUS_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    product.status = PRODUCT_STATUS_ENUM.PAUSED;
    await product.save();

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  @OnEvent(EVENT_ENUM.UPDATE_SOLD_QUANTITY_PRODUCT)
  async updateQuantityOrder(order: Order): Promise<void> {
    const { orderDetails } = order;

    const bulkWrites = orderDetails?.map((orderDetail) => {
      return {
        updateOne: {
          filter: { _id: orderDetail?.product },
          update: { $inc: { soldQuantity: orderDetail?.quantity } },
        },
      };
    });

    if (bulkWrites.length === 0) return;

    await this.productRepository.bulkWrite(bulkWrites);
  }

  async export(request: GetListProductRequestDto) {
    const { data: products } = await this.productRepository.list(request, true);

    if (isEmpty(products)) {
      throw new BusinessException(
        this.i18n.translate('error.NO_DATA_RECORDS'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const userCreatedByIdsSet = new Set<string>();
    products?.forEach((product) => {
      if (product.createdBy) {
        userCreatedByIdsSet.add(product.createdBy.toString());
      }
    });

    const userCreates = await this.userRepository.find({
      _id: {
        $in: [...userCreatedByIdsSet],
      },
    });

    const userMap = keyBy(userCreates, '_id');
    const productHeaders = createExportHeaders(
      this.i18n.translate('export.product.headers'),
    );
    const statusMap = this.i18n.translate('export.product.statusMap');
    const productSheetName = this.i18n.translate('export.product.title');

    products?.forEach((product) => {
      const category = product.category as any;
      const createdBy = userMap[product.createdBy?.toString()];
      product['soldQuantity'] = formatNumber(
        product['soldQuantity'] || 0,
        0,
      ) as any;
      product['createdBy'] = formatStringExport(
        createdBy?.fullname,
        createdBy?.email,
      );
      product['status'] = statusMap[product['status']] as any;
      product['price'] = formatNumber(product['price'] || 0) as any;
      product['createdAt'] = formatDateVN(product['createdAt']) as any;
      product['category'] = formatStringExport(category?.code, category?.name);
    });

    const workbook = exportOneSheetUtil(
      products,
      productSheetName,
      productHeaders,
      productSheetName,
    );

    const file = await workbook.xlsx.writeBuffer();
    // await workbook.xlsx.writeFile(`./product.xlsx`);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('error.SUCCESS'))
      .withData(file)
      .build();
  }
}
