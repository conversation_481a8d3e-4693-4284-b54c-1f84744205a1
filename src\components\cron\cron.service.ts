import * as fs from 'fs';
import * as path from 'path';
import { Connection } from 'mongoose';
import * as child_process from 'child_process';
import { InjectConnection } from '@nestjs/mongoose';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Inject, Injectable, Logger } from '@nestjs/common';

import {
  ORDER_STATUS_ENUM,
  PAYMENT_METHOD_ENUM,
  PAYMENT_STATUS_ENUM,
  ORDER_HISTORY_ACTION_ENUM,
} from '@components/order/order.constant';
import { EVENT_ENUM } from '@constant/event.enum';
import { generateRandomString } from '@helpers/string.helper';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { CRON_EXPRESSION } from '@components/cron/cron.constant';
import { TelegramService } from '@components/telegram/telegram.service';
import { PRODUCT_STATUS_ENUM } from '@components/product/product.constant';
import { UserRepositoryInterface } from '@database/repository/user/user.repository.interface';
import { OrderRepositoryInterface } from '@database/repository/order/order.repository.interface';
import { ProductRepositoryInterface } from '@database/repository/product/product.repository.interface';

@Injectable()
export class CronService {
  private readonly logger = new Logger(CronService.name);

  private readonly userIdAdmin = '67e2d33e8ca7e3889f8994b4';

  private readonly listReasonCancelOrder = [
    'Cửa hàng hết đồ chế biến',
    'Khách hàng đã yêu cầu hủy đơn hàng',
    'Thông tin đặt hàng không chính xác',
    'Lỗi kỹ thuật từ hệ thống xử lý đơn hàng',
    'Địa chỉ giao hàng nằm ngoài khu vực hỗ trợ',
    'Đơn hàng không đạt giá trị tối thiểu theo yêu cầu',
    'Phương thức thanh toán gặp vấn đề hoặc không hợp lệ',
    'Sản phẩm đã hết chưa được cập nhật trạng thái kịp thời',
    'Thời gian đặt hàng vượt quá khung giờ phục vụ của cửa hàng',
    'Khách hàng không xác nhận đơn hàng trong thời gian quy định',
  ];

  constructor(
    @InjectConnection()
    private readonly connection: Connection,

    private readonly eventEmitter: EventEmitter2,

    private readonly telegramService: TelegramService,

    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,

    @Inject('OrderRepositoryInterface')
    private readonly orderRepository: OrderRepositoryInterface,

    @Inject('ProductRepositoryInterface')
    private readonly productRepository: ProductRepositoryInterface,
  ) {}

  // @Cron(CronExpression.EVERY_10_SECONDS)
  @Cron(CronExpression.EVERY_12_HOURS)
  async backupDatabase() {
    this.logger.log(`Start backup database ${new Date().toLocaleTimeString()}`);

    const scriptPath = path.resolve(
      __dirname,
      '..', // Ra khỏi thư mục `cron`
      '..', // Ra khỏi `components`
      '..', // Ra khỏi `dist`
      'dump',
      process.platform === 'win32' ? 'dump.bat' : 'dump.sh',
    );

    child_process.exec(scriptPath, (error, stdout, stderr) => {
      if (error) {
        this.logger.error(`Backup database error: ${error.message}`);
        return;
      }

      if (stderr) {
        this.logger.warn(`Backup database warning: ${stderr}`);
      }

      this.logger.log(`Backup database success: ${stdout}`);

      // Gọi hàm xử lý tiếp theo nếu backup thành công
      this.onBackupSuccess();
    });
  }

  private onBackupSuccess() {
    this.logger.log('Executing follow-up action after successful backup...');

    try {
      // Lấy ra file backup mới nhất
      const dataDir = path.resolve(
        __dirname,
        '..', // Ra khỏi thư mục `cron`
        '..', // Ra khỏi `components`
        '..', // Ra khỏi `dist`
        'dump',
        'data',
      );

      // Lấy danh sách các file trong thư mục data
      const files = fs.readdirSync(dataDir);

      // Sắp xếp theo thời gian tạo để lấy file mới nhất
      const latestFile = files
        .filter((file) => file.endsWith('.archive'))
        .map((file) => ({
          name: file,
          time: fs.statSync(path.join(dataDir, file)).mtime.getTime(),
        }))
        .sort((a, b) => b.time - a.time)[0];

      if (latestFile) {
        const latestFilePath = path.join(dataDir, latestFile.name);
        this.logger.log(`Processing latest backup file: ${latestFilePath}`);

        // Gọi service Telegram để mã hóa và gửi file
        this.telegramService.processAndSendBackup(latestFilePath);
      } else {
        this.logger.warn('No backup files found in data directory');
      }
    } catch (error) {
      this.logger.error(`Error in onBackupSuccess: ${error.message}`);
    }
  }

  @Cron(CRON_EXPRESSION.EVERY_2_MINUTES)
  async sendLogsToTelegram() {
    try {
      this.logger.log('Starting sending logs to Telegram...');
      await this.telegramService.sendLogsToTelegram();
      this.logger.log('Finished sending logs to Telegram');
    } catch (error) {
      this.logger.error('Error while sending logs to Telegram:', error);
    }
  }

  @Cron(CRON_EXPRESSION.EVERY_5_MINUTES)
  // @Cron(CronExpression.EVERY_10_SECONDS)
  async updateStatusOrder() {
    try {
      this.logger.log('Starting order status update process...');

      // Xử lý các đơn hàng quá hạn thanh toán (15 phút)
      const fifteenMinutesAgo = new Date();
      fifteenMinutesAgo.setMinutes(fifteenMinutesAgo.getMinutes() - 15);

      // Tìm và hủy các đơn hàng PENDING, chưa thanh toán và quá 15 phút
      const expiredOrders = await this.orderRepository.find({
        status: ORDER_STATUS_ENUM.PENDING,
        paymentStatus: PAYMENT_STATUS_ENUM.UNPAID,
        createdAt: { $lt: fifteenMinutesAgo },
      });

      if (expiredOrders && expiredOrders.length > 0) {
        this.logger.log(
          `Found ${expiredOrders.length} expired orders to cancel`,
        );

        const expiredSession = await this.connection.startSession();
        expiredSession.startTransaction();

        try {
          const bulkWrites = expiredOrders.map((order) => {
            return {
              updateOne: {
                filter: { _id: order._id },
                update: {
                  status: ORDER_STATUS_ENUM.CANCELED,
                  $push: {
                    orderHistories: {
                      action: ORDER_HISTORY_ACTION_ENUM.CANCELLED,
                      description: 'Hệ thống huỷ do quá thời gian thanh toán',
                      createdAt: new Date(),
                    },
                  },
                },
              },
            };
          });

          if (bulkWrites.length !== 0) {
            await this.orderRepository.bulkWrite(bulkWrites, {
              session: expiredSession,
            });
          }

          await expiredSession.commitTransaction();
          this.logger.log('Expired orders canceled successfully');
        } catch (error) {
          this.logger.error(`Error canceling expired orders: ${error}`);
          await expiredSession.abortTransaction();
        } finally {
          await expiredSession.endSession();
        }
      }

      // Lấy các đơn hàng ở trạng thái PENDING (chưa thanh toán, phương thức COD), PREPARING, SHIPPING
      const orders = await this.orderRepository.find({
        $or: [
          { status: ORDER_STATUS_ENUM.SHIPPING },
          { status: ORDER_STATUS_ENUM.PREPARING },
          {
            status: ORDER_STATUS_ENUM.PENDING,
            paymentMethod: PAYMENT_METHOD_ENUM.COD,
            paymentStatus: PAYMENT_STATUS_ENUM.UNPAID,
          },
          {
            status: ORDER_STATUS_ENUM.PENDING,
            paymentMethod: PAYMENT_METHOD_ENUM.BANK,
            paymentStatus: PAYMENT_STATUS_ENUM.PAID,
          },
          {
            status: ORDER_STATUS_ENUM.PENDING,
            paymentMethod: PAYMENT_METHOD_ENUM.COD,
            paymentStatus: PAYMENT_STATUS_ENUM.PAID,
          },
          {
            status: ORDER_STATUS_ENUM.PENDING,
            paymentStatus: PAYMENT_STATUS_ENUM.PAID,
            paymentMethod: PAYMENT_METHOD_ENUM.PREPAID,
          },
        ],
      });

      if (!orders || orders.length === 0) {
        this.logger.log('No orders to update');
        return;
      }

      const shippers = await this.userRepository
        .find({
          role: USER_ROLE_ENUM.SHIPPER,
        })
        .limit(10);

      const shipperIds = shippers.map((shipper) => shipper._id);

      const randomShipperId =
        shipperIds[Math.floor(Math.random() * shipperIds.length)];

      // Lấy ngẫu nhiên 30-70% đơn hàng để cập nhật
      const percentage = Math.floor(Math.random() * 41) + 30; // Random từ 30-70%
      const totalOrdersToUpdate = Math.ceil(orders.length * (percentage / 100));

      // Shuffle mảng orders để lấy ngẫu nhiên
      const shuffledOrders = orders.sort(() => 0.5 - Math.random());
      const ordersToUpdate = shuffledOrders.slice(0, totalOrdersToUpdate);

      this.logger.log(
        `Updating ${totalOrdersToUpdate} orders (${percentage}% of ${orders.length})`,
      );

      const session = await this.connection.startSession();
      session.startTransaction();

      try {
        // Cập nhật từng đơn hàng
        for (const order of ordersToUpdate) {
          let newStatus: ORDER_STATUS_ENUM;
          let action: ORDER_HISTORY_ACTION_ENUM;
          let description: string;

          switch (order.status) {
            case ORDER_STATUS_ENUM.PENDING:
              // Đối với PENDING, 80% chuyển sang PREPARING, 20% từ chối (nếu là COD)
              if (
                order.paymentMethod === PAYMENT_METHOD_ENUM.COD &&
                Math.random() < 0.2
              ) {
                newStatus = ORDER_STATUS_ENUM.REJECTED;
                action = ORDER_HISTORY_ACTION_ENUM.REJECTED;
                // Lấy ngẫu nhiên một lý do từ chối
                description =
                  this.listReasonCancelOrder[
                    Math.floor(
                      Math.random() * this.listReasonCancelOrder.length,
                    )
                  ];
              } else {
                newStatus = ORDER_STATUS_ENUM.PREPARING;
                action = ORDER_HISTORY_ACTION_ENUM.CONFIRMED;
              }
              break;

            case ORDER_STATUS_ENUM.PREPARING:
              newStatus = ORDER_STATUS_ENUM.SHIPPING;
              action = ORDER_HISTORY_ACTION_ENUM.DELIVERING;
              break;

            case ORDER_STATUS_ENUM.SHIPPING:
              newStatus = ORDER_STATUS_ENUM.DELIVERED;
              action = ORDER_HISTORY_ACTION_ENUM.COMPLETED;
              break;

            default:
              continue; // Bỏ qua nếu không phải các trạng thái cần xử lý
          }

          // Cập nhật trạng thái đơn hàng
          const updateData: any = {
            urlPayment: '',
            status: newStatus,
            $push: {
              orderHistories: {
                action,
                createdAt: new Date(),
              },
            },
          };

          if (newStatus === ORDER_STATUS_ENUM.SHIPPING) {
            updateData['shipper'] = randomShipperId;
          }

          if (newStatus === ORDER_STATUS_ENUM.DELIVERED) {
            updateData['completedAt'] = new Date();
            // updateData['completedAt'] = order.createdAt;
            updateData['paymentStatus'] = PAYMENT_STATUS_ENUM.PAID;
          }

          // Thêm mô tả nếu có
          if (description) {
            updateData.$push.orderHistories.description = description;
          }

          await this.orderRepository.updateOne({ _id: order._id }, updateData, {
            session,
          });

          this.logger.log(
            `Updated order ${order.code} from ${ORDER_STATUS_ENUM[order.status]} to ${ORDER_STATUS_ENUM[newStatus]}`,
          );

          if (newStatus === ORDER_STATUS_ENUM.DELIVERED) {
            this.eventEmitter.emit(
              EVENT_ENUM.UPDATE_SOLD_QUANTITY_PRODUCT,
              order,
            );
            this.eventEmitter.emit(EVENT_ENUM.SYNC_DATA_ORDER_SUMMARY, order);
          }
        }

        await session.commitTransaction();
        this.logger.log('Order status update process completed successfully');
      } catch (error) {
        this.logger.error(`Error updating order statuses: ${error}`);
        await session.abortTransaction();
      } finally {
        await session.endSession();
      }
    } catch (error) {
      this.logger.error(`[UPDATE STATUS ORDER ERROR]: ${error}`);
    }
  }

  @Cron(CRON_EXPRESSION.EVERY_3_MINUTES)
  // @Cron(CronExpression.EVERY_10_SECONDS)
  async autoCreateOrder() {
    try {
      this.logger.log('Starting auto-create order process...');

      const listKey = [
        'name',
        'code',
        'price',
        'category',
        'updatedAt',
        'createdAt',
        'description',
        'soldQuantity',
      ];

      const sortRandom = listKey.map((key) => ({
        [key]: Math.random() < 0.5 ? 1 : -1,
      }));

      const sort = sortRandom[
        Math.floor(Math.random() * sortRandom.length)
      ] as any;

      // Lấy danh sách các sản phẩm có sẵn trong kho
      const products = await this.productRepository
        .find({
          status: PRODUCT_STATUS_ENUM.ACTIVE,
        })
        .sort(sort)
        .limit(Math.floor(Math.random() * 5) + 1);

      if (products.length === 0) {
        this.logger.log('No products available');
        return;
      }

      const orderDetails = products.map((item) => {
        const quantityOrder = Math.floor(Math.random() * 5) + 1;
        return {
          price: item.price,
          product: item._id,
          quantity: quantityOrder,
          totalPrice: item.price * quantityOrder,
        };
      });

      const totalPriceOrder = orderDetails.reduce(
        (total, item) => total + item.totalPrice,
        0,
      );

      const order = {
        code: generateRandomString(6),
        user: this.userIdAdmin,
        note: `Đơn hàng tự động ${new Date().toLocaleDateString()}`,
        phone: '0909090909',
        address: 'Xuân Phương, Nam Từ Liêm Hà Nội',
        recipientName: 'Đỗ Ngọc Giang',
        totalPrice: totalPriceOrder,
        orderDetails,
        paymentMethod: PAYMENT_METHOD_ENUM.COD,
        orderHistories: [
          {
            action: ORDER_HISTORY_ACTION_ENUM.CREATED,
            createdAt: new Date(),
          },
        ],
      };

      const newOrder = await this.orderRepository.create(order);

      this.logger.log(`[AUTO CREATE ORDER]: ${newOrder.code}`);
    } catch (error) {
      this.logger.error(`[AUTO CREATE ORDER ERROR]: ${error}`);
    }
  }
}
