import {
  <PERSON><PERSON>num,
  Is<PERSON>rray,
  IsString,
  <PERSON><PERSON>ength,
  IsNotEmpty,
  IsOptional,
  ArrayUnique,
  ArrayMinSize,
  IsPhoneNumber,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import { BaseDto } from '@core/dto/base.request.dto';
import { PAYMENT_METHOD_ENUM } from '@components/order/order.constant';

export class CreateOrderRequestDto extends BaseDto {
  @ApiPropertyOptional()
  @MaxLength(255)
  @IsString()
  @IsOptional()
  note: string;

  @ApiProperty()
  @MaxLength(255)
  @IsString()
  @IsNotEmpty()
  recipientName: string;

  @ApiProperty()
  @MaxLength(255)
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiProperty()
  @MaxLength(10)
  @IsPhoneNumber('VN')
  @IsString()
  @IsNotEmpty()
  phone: string;

  @ApiPropertyOptional()
  @IsEnum(PAYMENT_METHOD_ENUM)
  @IsOptional()
  paymentMethod: PAYMENT_METHOD_ENUM;

  @ApiProperty()
  @ArrayUnique()
  @IsArray()
  @ArrayMinSize(1)
  cartDetails: string[];
}
