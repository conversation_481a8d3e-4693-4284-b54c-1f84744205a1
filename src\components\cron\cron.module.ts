import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';

import { CronService } from './cron.service';
import { CronController } from './cron.controller';
import { TelegramService } from '@components/telegram/telegram.service';

@Module({
  imports: [ScheduleModule.forRoot()],
  controllers: [CronController],
  providers: [CronService, TelegramService],
  exports: [CronService],
})
export class CronModule {}
