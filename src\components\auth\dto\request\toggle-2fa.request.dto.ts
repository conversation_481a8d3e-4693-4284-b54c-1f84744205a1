import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, ValidateIf } from 'class-validator';

import { BaseDto } from '@core/dto/base.request.dto';
import { IS_2FA_ENUM } from '@components/user/user.constant';

export class Toggle2FARequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsEnum(IS_2FA_ENUM)
  is2FA: IS_2FA_ENUM;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @ValidateIf((o) => o.is2FA === IS_2FA_ENUM.ENABLED)
  otp: string;
}
