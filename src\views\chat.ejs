<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        #loginForm {
            margin-bottom: 20px;
            padding: 20px;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        #chatContainer {
            display: none;
            border-radius: 5px;
        }
        .chat-header {
            padding: 15px;
            background: #2196f3;
            color: white;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .chat-wrapper {
            display: flex;
            height: 600px;
            background: #fff;
            border: 1px solid #ddd;
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
        }
        #usersList {
            width: 250px;
            border-right: 1px solid #ddd;
            overflow-y: auto;
            background: #f5f5f5;
            display: none; /* Mặc định ẩn, chỉ hiển thị với admin */
        }
        .user-item {
            padding: 10px 15px;
            border-bottom: 1px solid #ddd;
            cursor: pointer;
        }
        .user-item:hover {
            background: #e3f2fd;
        }
        .user-item.active {
            background: #bbdefb;
        }
        .chat-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        #messageArea {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            background: #fff;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            max-width: 70%;
            position: relative;
        }
        .message-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        .message-content {
            word-wrap: break-word;
        }
        .message-time {
            font-size: 11px;
            color: #888;
            margin-top: 4px;
            text-align: right;
        }
        .sent {
            background: #e3f2fd;
            margin-left: auto;
            border-bottom-right-radius: 0;
        }
        .received {
            background: #f5f5f5;
            margin-right: auto;
            border-bottom-left-radius: 0;
        }
        #messageForm {
            display: flex;
            padding: 10px;
            border-top: 1px solid #ddd;
            background: #f9f9f9;
        }
        input[type="text"], input[type="email"], input[type="password"] {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        button {
            padding: 10px 16px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #1976d2;
        }
        #userInfo {
            padding: 10px 15px;
            background: #e3f2fd;
            border-bottom: 1px solid #ddd;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .empty-state {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        #selectedUser {
            font-weight: bold;
        }
        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            object-fit: cover;
        }
        .user-item-content {
            display: flex;
            align-items: center;
        }
        .user-details {
            flex: 1;
        }
        .user-name {
            font-weight: bold;
        }
        .user-email {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div id="loginForm">
        <h2>Đăng nhập HAUI Food - Chat</h2>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" placeholder="Nhập email của bạn">
        </div>
        <div class="form-group">
            <label for="password">Mật khẩu:</label>
            <input type="password" id="password" placeholder="Nhập mật khẩu của bạn">
        </div>
        <button onclick="login()">Đăng nhập</button>
    </div>

    <div id="chatContainer">
        <div class="chat-header">
            <h2>Trung tâm hỗ trợ HAUI Food</h2>
            <div id="userInfo"></div>
        </div>
        <div class="chat-wrapper">
            <div id="usersList">
                <div id="selectedUser">Chọn người dùng để chat</div>
                <div id="onlineUsersContainer">
                    <div class="spinner"></div>
                </div>
            </div>
            <div class="chat-content">
                <div id="messageArea">
                    <div class="empty-state">
                        <p>Chào mừng đến với trung tâm hỗ trợ HAUI Food</p>
                        <p>Hãy gửi tin nhắn cho chúng tôi nếu bạn cần hỗ trợ</p>
                    </div>
                </div>
                <form id="messageForm" onsubmit="sendMessage(event)">
                    <input type="text" id="messageInput" placeholder="Nhập tin nhắn của bạn...">
                    <button type="submit">Gửi</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        let socket;
        let userData;
        let token;
        let selectedUserId = null;
        const socketUrl = '<%= socketUrl %>';
        const DEBUG = true; // Bật chế độ debug

        function debugLog(...args) {
            if (DEBUG) {
                console.log(...args);
            }
        }

        async function login() {
            debugLog('Bắt đầu đăng nhập');
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                alert('Vui lòng nhập email và mật khẩu');
                return;
            }

            try {
                debugLog('Gọi API đăng nhập với email:', email);
                // Gọi API đăng nhập
                const loginResponse = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                if (!loginResponse.ok) {
                    const errorData = await loginResponse.json();
                    throw new Error(errorData.message || 'Đăng nhập thất bại');
                }

                const loginData = await loginResponse.json();
                debugLog('Đăng nhập thành công, dữ liệu:', loginData);
                
                // Lưu thông tin người dùng và token
                userData = loginData.data.user;
                token = loginData.data.accessToken;
                
                debugLog('Thông tin người dùng:', userData);
                debugLog('Token:', token.substring(0, 20) + '...');
                
                // Kết nối socket
                connectSocket();
                
                // Lấy lịch sử chat
                fetchChatHistory();
                
            } catch (error) {
                console.error('Login error:', error);
                alert('Lỗi đăng nhập: ' + error.message);
            }
        }

        function connectSocket() {
            try {
                debugLog('Bắt đầu kết nối socket với URL:', `${socketUrl}/chat`);
                // Kết nối socket với token
                socket = io(`${socketUrl}/chat`, {
                    auth: { token },
                    transports: ['websocket', 'polling'],
                    reconnection: true,
                    reconnectionAttempts: 5,
                    reconnectionDelay: 1000,
                    timeout: 10000
                });

                socket.on('connect', () => {
                    debugLog('Socket đã kết nối thành công, ID:', socket.id);
                    document.getElementById('loginForm').style.display = 'none';
                    document.getElementById('chatContainer').style.display = 'block';
                    
                    const userInfoElement = document.getElementById('userInfo');
                    userInfoElement.textContent = `Đã đăng nhập: ${userData.fullname} (${userData.role === 1 ? 'Admin' : 'Người dùng'})`;
                    
                    // Hiển thị danh sách người dùng nếu là admin
                    if (userData.role === 1) {
                        document.getElementById('usersList').style.display = 'block';
                        debugLog('Người dùng là admin, yêu cầu danh sách users online');
                        socket.emit('getOnlineUsers');
                    }
                });

                socket.on('onlineUsers', (users) => {
                    debugLog('Nhận danh sách users online:', users);
                    displayOnlineUsers(users);
                });

                socket.on('newMessage', (message) => {
                    debugLog('Nhận tin nhắn mới:', message);
                    displayMessage(message);
                });

                socket.on('messageSent', (message) => {
                    debugLog('Tin nhắn đã gửi:', message);
                    updatePendingMessageWithSent(message);
                });

                socket.on('error', (error) => {
                    console.error('Socket error:', error);
                    alert('Lỗi: ' + (error.message || 'Lỗi không xác định'));
                });

                socket.on('connect_error', (error) => {
                    console.error('Connection error:', error);
                    alert('Lỗi kết nối: ' + error.message);
                    document.getElementById('loginForm').style.display = 'block';
                    document.getElementById('chatContainer').style.display = 'none';
                });

                socket.on('reconnect', (attemptNumber) => {
                    debugLog('Socket tái kết nối lần thứ:', attemptNumber);
                });

                socket.on('reconnect_error', (error) => {
                    console.error('Lỗi tái kết nối:', error);
                });

                socket.on('disconnect', (reason) => {
                    console.log('Ngắt kết nối:', reason);
                    alert('Đã mất kết nối với máy chủ: ' + reason);
                    document.getElementById('loginForm').style.display = 'block';
                    document.getElementById('chatContainer').style.display = 'none';
                });

                // Thử gửi lại tin nhắn nếu có sự cố kết nối, nhưng không phải tất cả các lỗi
                socket.io.on("reconnect", () => {
                    debugLog('Socket đã tái kết nối, ID mới:', socket.id);
                });
                
                // Nếu bị lỗi "transport error", thử chuyển sang polling
                socket.io.on("error", (error) => {
                    debugLog('Socket IO error:', error);
                    if (error && error.type === "TransportError") {
                        debugLog('Lỗi transport, thử chuyển sang polling');
                        socket.io.opts.transports = ['polling', 'websocket'];
                    }
                });
            } catch (error) {
                console.error('Lỗi khi thiết lập kết nối socket:', error);
                alert('Lỗi khi kết nối: ' + error.message);
            }
        }

        function displayOnlineUsers(users) {
            const container = document.getElementById('onlineUsersContainer');
            
            if (!users || users.length === 0) {
                container.innerHTML = '<div class="empty-state">Không có người dùng online</div>';
                return;
            }
            
            let html = '';
            users.forEach(user => {
                const avatarUrl = user.avatar || 'https://via.placeholder.com/40';
                html += `
                    <div class="user-item" onclick="selectUser('${user.userId}', '${user.fullname}')">
                        <div class="user-item-content">
                            <img src="${avatarUrl}" alt="${user.fullname}" class="avatar">
                            <div class="user-details">
                                <div class="user-name">${user.fullname}</div>
                                <div class="user-email">${user.email}</div>
                            </div>
                        </div>
                    </div>`;
            });
            
            container.innerHTML = html;
        }

        function selectUser(userId, fullname) {
            // Highlight selected user
            const userItems = document.querySelectorAll('.user-item');
            userItems.forEach(item => item.classList.remove('active'));
            
            const selectedItem = document.querySelector(`.user-item[onclick*="${userId}"]`);
            if (selectedItem) {
                selectedItem.classList.add('active');
            }
            
            selectedUserId = userId;
            document.getElementById('selectedUser').textContent = `Chat với: ${fullname}`;
            
            // Lấy lịch sử chat với user này
            fetchChatHistoryWithUser(userId);
        }

        async function fetchChatHistory() {
            try {
                // Nếu là admin, không lấy lịch sử chat ngay mà chờ chọn user
                if (userData.role === 1) {
                    return;
                }
                
                // Lấy lịch sử chat từ API cho người dùng
                const response = await fetch('/api/v1/chat/user/history?page=1&limit=50', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Không thể lấy lịch sử chat');
                }
                
                const data = await response.json();
                
                if (data.data && data.data.items && data.data.items.length > 0) {
                    displayChatHistory(data.data.items);
                }
            } catch (error) {
                console.error('Error fetching chat history:', error);
            }
        }
        
        async function fetchChatHistoryWithUser(userId) {
            try {
                // Chỉ admin mới có thể lấy lịch sử chat với user cụ thể
                if (userData.role !== 1) return;
                
                // Xóa các tin nhắn cũ
                document.getElementById('messageArea').innerHTML = '<div class="spinner"></div>';
                
                const response = await fetch(`/api/v1/chat/user/${userId}/history?page=1&limit=50`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Không thể lấy lịch sử chat');
                }
                
                const data = await response.json();
                
                // Xóa spinner
                document.getElementById('messageArea').innerHTML = '';
                
                if (data.data && data.data.items && data.data.items.length > 0) {
                    displayChatHistory(data.data.items);
                } else {
                    document.getElementById('messageArea').innerHTML = 
                        '<div class="empty-state">Chưa có tin nhắn nào với người dùng này</div>';
                }
            } catch (error) {
                console.error('Error fetching chat history with user:', error);
                document.getElementById('messageArea').innerHTML = 
                    '<div class="empty-state">Đã xảy ra lỗi khi lấy lịch sử chat</div>';
            }
        }
        function displayChatHistory(messages) {
            const messageArea = document.getElementById('messageArea');
            messageArea.innerHTML = '';
            
            messages?.reverse()?.forEach(msg => {
                // isUserMsg = 0 là tin nhắn của admin, isUserMsg = 1 là tin nhắn của user
                const isSent = userData.role === 1 ? msg.isUserMsg === 0 : msg.isUserMsg === 1;
                
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isSent ? 'sent' : 'received'}`;
                
                const timestamp = new Date(msg.createdAt).toLocaleString();
                
                messageDiv.innerHTML = `
                    <div class="message-info">${isSent ? 'Bạn' : (userData.role === 1 ? 'Người dùng' : 'Admin')}</div>
                    <div class="message-content">${msg.message}</div>
                    <div class="message-time">${timestamp}</div>
                `;
                
                messageArea.appendChild(messageDiv);
            });
            
            messageArea.scrollTop = messageArea.scrollHeight;
        }

        // Hàm kiểm tra kết nối socket
        function checkSocketConnection() {
            if (!socket || !socket.connected) {
                debugLog('Socket không kết nối, thử kết nối lại');
                if (token) {
                    // Thử kết nối lại socket nếu đã đăng nhập
                    connectSocket();
                    return false;
                } else {
                    alert('Bạn cần đăng nhập lại');
                    document.getElementById('loginForm').style.display = 'block';
                    document.getElementById('chatContainer').style.display = 'none';
                    return false;
                }
            }
            return true;
        }

        function sendMessage(event) {
            event.preventDefault();
            
            try {
                // Kiểm tra trạng thái socket
                debugLog('Trạng thái socket trước khi gửi tin nhắn:', {
                    socketExists: !!socket,
                    isConnected: socket?.connected,
                    socketId: socket?.id
                });
                
                if (!checkSocketConnection()) {
                    return; // Không thể gửi tin nhắn do không có kết nối
                }
                
                const messageInput = document.getElementById('messageInput');
                const message = messageInput.value.trim();
                
                if (!message) return;

                // Xác định người nhận
                let receiverId;
                
                if (userData.role === 1) {
                    // Admin cần chọn người nhận
                    if (!selectedUserId) {
                        alert('Vui lòng chọn người dùng để chat');
                        return;
                    }
                    receiverId = selectedUserId;
                } else {
                    // User luôn gửi cho admin
                    receiverId = 'admin';
                }

                debugLog('Gửi tin nhắn đến:', receiverId, 'Nội dung:', message);

                const messageData = {
                    message: message,
                    receiverId: receiverId
                };

                // Cache tin nhắn trong trình duyệt để có thể gửi lại nếu cần
                const pendingMessage = {
                    ...messageData,
                    timestamp: new Date(),
                    pendingId: Date.now().toString(),
                    retries: 0
                };
                
                // Hiển thị tin nhắn đang gửi với trạng thái "đang gửi"
                displayPendingMessage(pendingMessage);
                
                // Xóa nội dung input ngay sau khi hiển thị tin nhắn đang gửi
                messageInput.value = '';

                // Gửi tin nhắn với callback
                debugLog('Gửi event sendMessage với data:', messageData);
                
                // Thêm một biến để đánh dấu đã nhận phản hồi
                let responseReceived = false;
                
                socket.emit('sendMessage', messageData, (error, response) => {
                    responseReceived = true;
                    
                    if (error) {
                        debugLog('Lỗi khi gửi tin nhắn:', error);
                        updatePendingMessageStatus(pendingMessage.pendingId, 'error');
                    } else {
                        debugLog('Tin nhắn đã gửi thành công:', response);
                        // Không cần cập nhật trạng thái ở đây vì sẽ nhận event messageSent
                    }
                });
                
                // Thiết lập timeout để kiểm tra phản hồi
                setTimeout(() => {
                    if (!responseReceived) {
                        debugLog('Không nhận được phản hồi sau 5 giây, kiểm tra trạng thái socket');
                        if (socket.connected) {
                            // Socket vẫn kết nối nhưng không nhận được phản hồi
                            debugLog('Socket vẫn kết nối, nhưng không nhận được phản hồi');
                        } else {
                            // Socket đã ngắt kết nối
                            debugLog('Socket đã ngắt kết nối, đánh dấu tin nhắn lỗi');
                            updatePendingMessageStatus(pendingMessage.pendingId, 'error');
                        }
                    }
                }, 5000);
                
            } catch (error) {
                console.error('Lỗi khi gửi tin nhắn:', error);
                alert('Không thể gửi tin nhắn: ' + error.message);
            }
        }

        // Hiển thị tin nhắn đang chờ gửi
        function displayPendingMessage(pendingMessage) {
            const messageArea = document.getElementById('messageArea');
            
            // Xóa empty state nếu có
            const emptyState = messageArea.querySelector('.empty-state');
            if (emptyState) {
                messageArea.innerHTML = '';
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message sent pending';
            messageDiv.id = `pending-${pendingMessage.pendingId}`;
            
            // Hiển thị thời gian
            const timestamp = pendingMessage.timestamp.toLocaleString();
            
            messageDiv.innerHTML = `
                <div class="message-info">Bạn</div>
                <div class="message-content">${pendingMessage.message}</div>
                <div class="message-time">${timestamp} <span class="status">đang gửi...</span></div>
            `;
            
            messageArea.appendChild(messageDiv);
            messageArea.scrollTop = messageArea.scrollHeight;
        }

        // Cập nhật trạng thái tin nhắn đang chờ
        function updatePendingMessageStatus(pendingId, status) {
            const messageDiv = document.getElementById(`pending-${pendingId}`);
            if (messageDiv) {
                const statusSpan = messageDiv.querySelector('.status');
                if (statusSpan) {
                    if (status === 'sent') {
                        statusSpan.textContent = 'đã gửi';
                        statusSpan.style.color = 'green';
                    } else if (status === 'error') {
                        statusSpan.textContent = 'lỗi gửi';
                        statusSpan.style.color = 'red';
                    }
                }
            }
        }

        // Hàm mới để cập nhật tin nhắn đang chờ thành tin nhắn đã gửi
        function updatePendingMessageWithSent(message) {
            // Tìm tin nhắn đang chờ gần nhất
            const pendingMessages = document.querySelectorAll('.message.sent.pending');
            if (pendingMessages.length > 0) {
                // Lấy tin nhắn đang chờ cuối cùng (gần nhất)
                const lastPendingMessage = pendingMessages[pendingMessages.length - 1];
                
                // Cập nhật lớp CSS và ID
                lastPendingMessage.classList.remove('pending');
                
                // Cập nhật thời gian và trạng thái
                const timestamp = new Date(message.timestamp || message.createdAt).toLocaleString();
                const timeDiv = lastPendingMessage.querySelector('.message-time');
                if (timeDiv) {
                    // Chỉ hiển thị thời gian, không hiển thị trạng thái
                    timeDiv.innerHTML = timestamp;
                }
                
                return true; // Đã cập nhật thành công
            }
            
            // Nếu không tìm thấy tin nhắn đang chờ, hiển thị tin nhắn mới
            displayMessage(message, true);
            return false;
        }

        function displayMessage(message, isSent = false) {
            // Kiểm tra nếu là admin và có selectedUserId
            if (userData.role === 1 && selectedUserId) {
                // Nếu tin nhắn không liên quan đến người dùng đang được chọn thì bỏ qua
                if (!isSent && message.senderId !== selectedUserId) {
                    // Highlight user có tin nhắn mới
                    const userItem = document.querySelector(`.user-item[onclick*="${message.senderId}"]`);
                    if (userItem) {
                        userItem.style.backgroundColor = '#ffecb3';
                        setTimeout(() => {
                            userItem.style.backgroundColor = '';
                        }, 3000);
                    }
                    return;
                }
            }
            
            const messageArea = document.getElementById('messageArea');
            
            // Xóa empty state nếu có
            const emptyState = messageArea.querySelector('.empty-state');
            if (emptyState) {
                messageArea.innerHTML = '';
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isSent ? 'sent' : 'received'}`;
            
            // Hiển thị thời gian
            const timestamp = message.timestamp ? new Date(message.timestamp).toLocaleString() : new Date().toLocaleString();
            
            messageDiv.innerHTML = `
                <div class="message-info">${isSent ? 'Bạn' : message.senderName || 'Admin'}</div>
                <div class="message-content">${message.message}</div>
                <div class="message-time">${timestamp}</div>
            `;
            
            messageArea.appendChild(messageDiv);
            messageArea.scrollTop = messageArea.scrollHeight;
        }
    </script>
</body>
</html> 
