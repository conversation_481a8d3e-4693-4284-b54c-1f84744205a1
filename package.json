{"name": "haui-food-backend", "version": "0.0.1", "description": "", "author": "hauifood.com", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start swc --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@keyv/redis": "4.3.1", "@nest-lab/throttler-storage-redis": "^1.1.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "3.0.0", "@nestjs/common": "^11.0.12", "@nestjs/config": "^3.3.0", "@nestjs/core": "^11.0.12", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/mongoose": "^10.0.10", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.12", "@nestjs/platform-socket.io": "^11.0.2", "@nestjs/schedule": "^5.0.1", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.0.2", "axios": "^1.8.4", "bcrypt": "^5.1.1", "cache-manager": "6.4.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cloudinary": "^2.5.1", "colors": "^1.4.0", "crypto-js": "^4.2.0", "ejs": "^3.1.10", "exceljs": "^4.4.0", "ioredis": "^5.6.0", "lodash": "^4.17.21", "moment": "^2.30.1", "mongoose": "^8.6.3", "morgan": "^1.10.0", "nestjs-i18n": "^10.4.9", "node-2fa": "^2.0.3", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "slugify": "^1.6.6", "socket.io": "^4.8.1", "svg-captcha": "^1.4.0", "swagger-themes": "^1.4.3", "systeminformation": "^5.25.11", "telegraf": "^4.16.3"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@swc/cli": "^0.4.0", "@swc/core": "^1.7.26", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.21", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.13", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "swc-loader": "^0.2.6", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}