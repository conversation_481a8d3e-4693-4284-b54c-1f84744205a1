import * as si from 'systeminformation';
import { <PERSON>ron } from '@nestjs/schedule';
import { I18nService } from 'nestjs-i18n';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cache, CACHE_MANAGER } from '@nestjs/cache-manager';

import { ResponseBuilder } from '@utils/response-builder';
import {
  KEY_SYSTEM_INFO,
  TIME_CACHE_SYSTEM_INFO,
} from '@components/system-monitor/system-monitor.constant';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { CRON_EXPRESSION } from '@components/cron/cron.constant';

@Injectable()
export class SystemMonitorService {
  private readonly logger = new Logger(SystemMonitorService.name);
  constructor(
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,

    private readonly i18n: I18nService,
  ) {}

  async getSystemInfo() {
    try {
      const oldData = await this.cacheManager.get(KEY_SYSTEM_INFO);
      if (oldData) {
        return new ResponseBuilder(oldData)
          .withCode(ResponseCodeEnum.SUCCESS)
          .withMessage(this.i18n.translate('message.SUCCESS'))
          .build();
      }

      const data = await this.getInfoSystem();
      return new ResponseBuilder(data)
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(this.i18n.translate('message.SUCCESS'))
        .build();
    } catch (error) {
      throw new Error('Failed to fetch system information: ' + error.message);
    }
  }

  @Cron(CRON_EXPRESSION.EVERY_26_SECONDS)
  async getInfoSystem(): Promise<any> {
    this.logger.log('Getting system info...');
    try {
      const [cpu, cpuUsage, mem, disk, uptime] = await Promise.all([
        si.cpu(),
        si.currentLoad(),
        si.mem(),
        si.fsSize(),
        this.getSystemUptime(),
      ]);

      const diskSummary = disk.reduce(
        (acc, d) => {
          acc.total += d.size;
          acc.used += d.used;
          acc.free += d.size - d.used;
          return acc;
        },
        { total: 0, used: 0, free: 0 },
      );

      const data = {
        uptime: this.formatUptime(uptime), // Thời gian chạy của server
        cpu: {
          model: cpu.manufacturer + ' ' + cpu.brand,
          cores: cpu.cores,
          usage: cpuUsage.currentLoad.toFixed(2) + '%', // % CPU sử dụng
        },
        memory: {
          total: (mem.total / 1024 / 1024 / 1024).toFixed(2) + ' GB', // Tổng RAM
          used: (mem.used / 1024 / 1024 / 1024).toFixed(2) + ' GB', // RAM đã dùng
          free: (mem.free / 1024 / 1024 / 1024).toFixed(2) + ' GB', // RAM trống
          usage: ((mem.used / mem.total) * 100).toFixed(2) + '%', // % RAM sử dụng
        },
        disk: {
          total: (diskSummary.total / 1024 / 1024 / 1024).toFixed(2) + ' GB',
          used: (diskSummary.used / 1024 / 1024 / 1024).toFixed(2) + ' GB',
          free: (diskSummary.free / 1024 / 1024 / 1024).toFixed(2) + ' GB',
          usage:
            ((diskSummary.used / diskSummary.total) * 100).toFixed(2) + '%',
        },
      };

      await this.cacheManager.set(
        KEY_SYSTEM_INFO,
        data,
        TIME_CACHE_SYSTEM_INFO,
      );

      return data;
    } catch (error) {
      this.logger.error('Error updating system info:', error);
    }
  }

  private getSystemUptime(): number {
    try {
      const timeData = si.time();
      return timeData?.uptime || 0;
    } catch (error) {
      console.error('Error fetching system uptime:', error);
      throw new Error('Failed to fetch system uptime');
    }
  }

  private formatUptime(seconds: number): string {
    const days = Math.floor(seconds / (3600 * 24));
    seconds %= 3600 * 24;
    const hours = Math.floor(seconds / 3600);
    seconds %= 3600;
    const minutes = Math.floor(seconds / 60);

    return `${days}d ${hours}h ${minutes}m`;
  }
}
