import { Controller, Get, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { RoleGuard } from '@core/guards/role.guard';
import { Roles } from '@core/decorators/roles.decorator';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { SystemMonitorService } from './system-monitor.service';

@ApiBearerAuth()
@Controller('system-monitor')
export class SystemMonitorController {
  constructor(private readonly systemMonitorService: SystemMonitorService) {}

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN, USER_ROLE_ENUM.VIEWER)
  @Get('')
  @ApiOperation({ summary: 'L<PERSON>y thông tin hệ thống' })
  @ApiResponse({ status: 200, description: 'Thành công' })
  async getSystemInfo() {
    return await this.systemMonitorService.getSystemInfo();
  }
}
