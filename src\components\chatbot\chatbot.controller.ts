import { isEmpty } from 'lodash';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Body, Controller, Post, Request } from '@nestjs/common';

import { ChatbotService } from './chatbot.service';
import { BaseDto } from '@core/dto/base.request.dto';
import { RequestChatbotRequestDto } from './dto/request/request-chatbot.request.dto';

@Controller('chatbot')
export class ChatbotController {
  constructor(private readonly chatbotService: ChatbotService) {}

  @Post('/request')
  @ApiOperation({
    tags: ['Chat'],
    summary: 'Yêu cầu chatbot',
    description: 'Yêu cầu chatbot',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async requestChatbot(@Body() body: RequestChatbotRequestDto) {
    const { request, responseError } = body;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.chatbotService.requestOpenAI(request);
  }

  @Post('/reset')
  @ApiOperation({
    tags: ['Chat'],
    summary: 'Reset state đã chat với Chatbot',
    description: 'Reset state đã chat với Chatbot',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async resetState(@Request() request: BaseDto) {
    return await this.chatbotService.resetState(request);
  }
}
