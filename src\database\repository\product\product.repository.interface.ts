import { Product } from '@database/schemas/product.model';
import { BaseInterfaceRepository } from '@core/repository/base.interface.repository';
import { UpdateProductRequestDto } from '@components/product/dto/request/update-product.request.dto';
import { CreateProductRequestDto } from '@components/product/dto/request/create-product.request.dto';
import { GetListProductRequestDto } from '@components/product/dto/request/get-list-product.request.dto';

export interface ProductRepositoryInterface
  extends BaseInterfaceRepository<Product> {
  createEntity(data: CreateProductRequestDto): Product;

  getDetail(id: string): Promise<Product | null>;

  updateEntity(entity: Product, data: UpdateProductRequestDto): Product;

  list(
    request: GetListProductRequestDto,
    isExport?: boolean,
  ): Promise<{ data: Product[]; total: number }>;

  findByKeywords(keywords: string[]): Promise<Product[]>;

  findPriceInfoByKeywords(keywords: string[]): Promise<Product[]>;

  findFeatured(): Promise<Product[]>;
}
