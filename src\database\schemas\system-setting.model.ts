import * as mongoose from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseModel } from '@core/schema/base.model';

@Schema({
  timestamps: true,
  collection: 'system_settings',
})
export class SystemSetting extends BaseModel {
  @Prop({
    type: String,
    required: true,
    unique: true,
  })
  key: string;

  @Prop({
    type: mongoose.Schema.Types.Mixed,
    required: false,
  })
  value: any;

  @Prop({
    type: String,
    required: false,
  })
  description?: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false,
  })
  createdBy: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false,
  })
  updatedBy: string;
}

export const SystemSettingSchema = SchemaFactory.createForClass(SystemSetting);

export type SystemSettingDocument = SystemSetting & mongoose.Document;
