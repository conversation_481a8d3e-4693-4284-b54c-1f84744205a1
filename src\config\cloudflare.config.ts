import { IsString } from 'class-validator';
import { registerAs } from '@nestjs/config';

import { CloudflareConfig } from './config.type';
import validateConfig from '@utils/validate-config';

class EnvironmentVariablesValidator {
  @IsString()
  CLOUDFLARE_TURNSTILE_SECRET_KEY: string;
}

export default registerAs<CloudflareConfig>('cloudflare', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);
  return {
    turnstileSecretKey: process.env.CLOUDFLARE_TURNSTILE_SECRET_KEY,
  };
});
