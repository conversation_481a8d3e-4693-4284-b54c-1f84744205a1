import { Modu<PERSON> } from '@nestjs/common';

import { FileService } from './file.service';
import { FileController } from './file.controller';
import { FileProvider } from '@components/file/file.provider';
import { CloudflareModule } from '@components/cloudflare/cloudflare.module';

@Module({
  imports: [CloudflareModule],
  controllers: [FileController],
  providers: [FileProvider, FileService],
  exports: [FileService],
})
export class FileModule {}
