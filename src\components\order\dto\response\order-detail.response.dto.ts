import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

import { BaseResponseDto } from '@core/dto/base.response.dto';
import { GetUserDetailResponseDto } from '@components/user/dto/response/get-user-detail.response.dto';

export class CategoryResponseDto extends BaseResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  slug: string;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  image: string;
}

export class ProductResponseDto extends BaseResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  slug: string;

  @ApiProperty()
  @Expose()
  soldQuantity: number;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty()
  @Expose()
  price: number;

  @ApiProperty()
  @Expose()
  description: string;

  @ApiProperty()
  @Expose()
  image: string;

  @ApiProperty({ type: CategoryResponseDto })
  @Expose()
  @Type(() => CategoryResponseDto)
  category: CategoryResponseDto;
}

export class OrderDetailResponseDto extends BaseResponseDto {
  @ApiProperty()
  @Expose()
  quantity: number;

  @ApiProperty()
  @Expose()
  price: number;

  @ApiProperty()
  @Expose()
  totalPrice: number;

  @ApiProperty({ type: ProductResponseDto })
  @Expose()
  @Type(() => ProductResponseDto)
  product: ProductResponseDto;
}

export class OrderHistoryResponseDto extends BaseResponseDto {
  @ApiProperty()
  @Expose()
  action: number;

  @ApiProperty()
  @Expose()
  description: string;
}

export class GetDetailOrderResponseDto extends BaseResponseDto {
  @ApiProperty({ type: GetUserDetailResponseDto })
  @Expose()
  @Type(() => GetUserDetailResponseDto)
  user: GetUserDetailResponseDto;

  @ApiProperty({ type: GetUserDetailResponseDto })
  @Expose()
  @Type(() => GetUserDetailResponseDto)
  shipper: GetUserDetailResponseDto;

  @ApiProperty()
  @Expose()
  totalPrice: number;

  @ApiProperty()
  @Expose()
  paymentMethod: number;

  @ApiProperty()
  @Expose()
  paymentStatus: number;

  @ApiProperty()
  @Expose()
  urlPayment: string;

  @ApiProperty()
  @Expose()
  status: number;

  @ApiProperty()
  @Expose()
  recipientName: string;

  @ApiProperty()
  @Expose()
  address: string;

  @ApiProperty()
  @Expose()
  phone: string;

  @ApiProperty()
  @Expose()
  note: string;

  @ApiProperty({ type: [OrderDetailResponseDto] })
  @Expose()
  @Type(() => OrderDetailResponseDto)
  orderDetails: OrderDetailResponseDto[];

  @ApiProperty({ type: [OrderHistoryResponseDto] })
  @Expose()
  @Type(() => OrderHistoryResponseDto)
  orderHistories: OrderHistoryResponseDto[];
}
