import * as moment from 'moment';
import { isEmpty } from 'lodash';
import { InjectModel } from '@nestjs/mongoose';
import { Model, PipelineStage, Types } from 'mongoose';

import { Product } from '@database/schemas/product.model';
import { ProductRepositoryInterface } from './product.repository.interface';
import { convertOrderMongo, getRegexByValue, SortOrder } from '@utils/common';
import { BaseAbstractRepository } from '@core/repository/base.abstract.repository';
import { UpdateProductRequestDto } from '@components/product/dto/request/update-product.request.dto';
import { CreateProductRequestDto } from '@components/product/dto/request/create-product.request.dto';
import { GetListProductRequestDto } from '@components/product/dto/request/get-list-product.request.dto';

export class ProductRepository
  extends BaseAbstractRepository<Product>
  implements ProductRepositoryInterface
{
  constructor(
    @InjectModel('Product')
    private readonly productModel: Model<Product>,
  ) {
    super(productModel);
  }

  createEntity(data: CreateProductRequestDto): Product {
    const entity = new this.productModel();

    entity.name = data.name;
    entity.code = data.code;
    entity.slug = data.slug;
    entity.price = data.price;
    entity.image = data.image;
    entity.category = data.category;
    entity.description = data.description;
    entity.createdBy = data.userId;

    return entity;
  }

  updateEntity(entity: Product, data: UpdateProductRequestDto): Product {
    entity.name = data.name;
    entity.slug = data.slug;
    entity.price = data.price;
    entity.image = data.image;
    entity.status = data.status;
    entity.description = data.description;

    return entity;
  }

  async getDetail(id: string): Promise<Product | null> {
    return await this.productModel.findOne({ _id: id, deletedAt: null }).exec();
  }

  async list(
    request: GetListProductRequestDto,
    isExport = false,
  ): Promise<{ data: Product[]; total: number }> {
    const { keyword, sort, filter, page, limit } = request;

    const take = limit;
    const skip = (page - 1) * limit;

    let filterObj: any = {};
    let sortObj: any = { createdAt: SortOrder.DESC };

    if (!isEmpty(keyword)) {
      const filterByKeyword = getRegexByValue(keyword);
      filterObj = {
        $or: [
          { name: filterByKeyword },
          { slug: filterByKeyword },
          { code: filterByKeyword },
        ],
      };
    }

    if (!isEmpty(filter)) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item.column) {
          case 'keyword':
            filterObj = {
              ...filterObj,
              $or: [
                { name: getRegexByValue(value) },
                { code: getRegexByValue(value) },
                { slug: getRegexByValue(value) },
              ],
            };
            break;
          case 'name':
            filterObj = {
              ...filterObj,
              name: getRegexByValue(value),
            };
            break;
          case 'code':
            filterObj = {
              ...filterObj,
              code: getRegexByValue(value),
            };
            break;
          case 'slug':
            filterObj = {
              ...filterObj,
              slug: getRegexByValue(value),
            };
            break;
          case 'price':
            const [startPrice, endPrice] = value?.split('|')?.map(Number);
            filterObj = {
              ...filterObj,
              price: {
                $lte: endPrice,
                $gte: startPrice,
              },
            };
            break;
          case 'status':
            filterObj = {
              ...filterObj,
              status: {
                $in: value?.split(',')?.map(Number),
              },
            };
            break;
          case 'categories':
            filterObj = {
              ...filterObj,
              category: {
                $in: value?.split(',')?.map((id) => new Types.ObjectId(id)),
              },
            };
            break;
          case 'createdAt':
            const [startCreateAt, endCreateAt] = value?.split('|');
            filterObj = {
              ...filterObj,
              createdAt: {
                $lte: moment(endCreateAt).endOf('day').toDate(),
                $gte: moment(startCreateAt).startOf('day').toDate(),
              },
            };
            break;
          case 'updatedAt':
            const [startUpdateAt, endUpdateAt] = value?.split('|');
            filterObj = {
              ...filterObj,
              updatedAt: {
                $lte: moment(endUpdateAt).endOf('day').toDate(),
                $gte: moment(startUpdateAt).startOf('day').toDate(),
              },
            };
            break;
          default:
            break;
        }
      });
    }

    if (!isEmpty(sort)) {
      sort.forEach((item) => {
        const order = convertOrderMongo(item.order);
        switch (item.column) {
          case 'name':
            sortObj = { ...sortObj, name: order };
            break;
          case 'code':
            sortObj = { ...sortObj, code: order };
            break;
          case 'slug':
            sortObj = { ...sortObj, slug: order };
            break;
          case 'price':
            sortObj = { ...sortObj, price: order };
            break;
          case 'soldQuantity':
            sortObj = { ...sortObj, soldQuantity: order };
            break;
          case 'status':
            sortObj = { ...sortObj, status: order };
            break;
          case 'createdAt':
            sortObj = { ...sortObj, createdAt: order };
            break;
          case 'updatedAt':
            sortObj = { ...sortObj, updatedAt: order };
            break;
          default:
            break;
        }
      });
    }

    const pipeline: any[] = [
      { $match: { deletedAt: null, ...filterObj } },
      { $sort: sortObj },
      {
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'category',
        },
      },
      { $unwind: '$category' },
    ];

    if (!isExport) {
      pipeline.push({ $skip: skip });
      pipeline.push({ $limit: take });
    }

    const [products, total] = await Promise.all([
      this.productModel.aggregate(pipeline),
      !isExport
        ? this.productModel
            .countDocuments({ deletedAt: null, ...filterObj })
            .exec()
        : 0,
    ]);
    return { data: products, total };
  }

  async findByKeywords(keywords: string[]): Promise<Product[]> {
    const keywordConditions = keywords.map((keyword) => ({
      $or: [
        { code: getRegexByValue(keyword) },
        { name: getRegexByValue(keyword) },
        { slug: getRegexByValue(keyword) },
        { description: getRegexByValue(keyword) },
      ],
    }));

    const pipeline: PipelineStage[] = [
      {
        $match: {
          deletedAt: null,
          $or: keywordConditions,
        },
      },
      {
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'category',
        },
      },
      { $unwind: '$category' },
      { $limit: 10 },
    ];

    return await this.productModel.aggregate(pipeline);
  }

  async findPriceInfoByKeywords(keywords: string[]): Promise<Product[]> {
    // Chỉ tìm kiếm trong name và code, bỏ qua slug vì slug được tạo từ name
    const keywordPattern = keywords
      .filter((k) => k.length >= 2)
      .map((k) => new RegExp(k, 'i'));

    const pipeline: PipelineStage[] = [
      {
        $match: {
          deletedAt: null,
          // Sử dụng $and để đảm bảo tất cả keywords đều match
          $and: keywordPattern.map((pattern) => ({
            $or: [{ name: pattern }, { code: pattern }],
          })),
        },
      },
      {
        $project: {
          name: 1,
          code: 1,
          price: 1,
          image: 1,
          category: 1,
          // Thêm trường để tính độ phù hợp
          relevanceScore: {
            $add: [
              { $cond: [{ $eq: ['$status', 1] }, 2, 0] }, // Ưu tiên sản phẩm đang bán
              { $cond: [{ $gt: ['$soldQuantity', 0] }, 1, 0] }, // Ưu tiên sản phẩm đã từng bán
            ],
          },
        },
      },
      {
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'category',
          // Chỉ lấy các trường cần thiết của category
          pipeline: [
            {
              $project: {
                name: 1,
                code: 1,
              },
            },
          ],
        },
      },
      { $unwind: '$category' },
      { $sort: { relevanceScore: -1 } },
      { $limit: 5 },
    ];

    return await this.productModel.aggregate(pipeline);
  }

  async findFeatured(): Promise<Product[]> {
    const pipeline: PipelineStage[] = [
      {
        $match: {
          deletedAt: null,
        },
      },
      {
        $lookup: {
          from: 'categories',
          localField: 'category',
          foreignField: '_id',
          as: 'category',
        },
      },
      { $unwind: '$category' },
      {
        $sort: {
          soldQuantity: -1,
        },
      },
      { $limit: 5 },
    ];

    return await this.productModel.aggregate(pipeline);
  }
}
