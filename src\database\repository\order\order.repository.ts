import * as moment from 'moment';
import { isEmpty } from 'lodash';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

import { BaseDto } from '@core/dto/base.request.dto';
import { Order } from '@database/schemas/order.model';
import { getStartAndEndTime } from '@helpers/date.helper';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { OrderRepositoryInterface } from './order.repository.interface';
import { convertOrderMongo, getRegexByValue, SortOrder } from '@utils/common';
import { StatisticByStatusType } from '@components/types/statistic-by-status.type';
import { BaseAbstractRepository } from '@core/repository/base.abstract.repository';
import { GetListOrderRequestDto } from '@components/order/dto/request/get-list-order.request.dto';
import { GetSummaryOrderRequestDto } from '@components/dashboard/dto/request/get-summary-order.request.dto';

export class OrderRepository
  extends BaseAbstractRepository<Order>
  implements OrderRepositoryInterface
{
  constructor(
    @InjectModel('Order')
    private readonly orderModel: Model<Order>,
  ) {
    super(orderModel);
  }

  async list(
    request: GetListOrderRequestDto,
    role?: USER_ROLE_ENUM,
  ): Promise<{ data: Order[]; total: number }> {
    const { keyword, sort, filter, page, limit } = request;

    const take = limit;
    const skip = (page - 1) * limit;

    let filterObj: any = {};
    let sortObj: any = {};

    if (!isEmpty(keyword)) {
      const filterByKeyword = getRegexByValue(keyword);
      filterObj = {
        $or: [
          { code: filterByKeyword },
          { phone: filterByKeyword },
          { address: filterByKeyword },
          { recipientName: filterByKeyword },
        ],
      };
    }

    if (!isEmpty(filter)) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item.column) {
          case 'note':
            filterObj = {
              ...filterObj,
              note: getRegexByValue(value),
            };
            break;
          case 'keyword':
            filterObj = {
              ...filterObj,
              $or: [
                { code: getRegexByValue(value) },
                { phone: getRegexByValue(value) },
                { address: getRegexByValue(value) },
                { recipientName: getRegexByValue(value) },
              ],
            };
            break;
          case 'address':
            filterObj = {
              ...filterObj,
              address: getRegexByValue(value),
            };
            break;
          case 'totalPrice':
            const [startTotalPrice, endTotalPrice] = item.text
              ?.split('|')
              ?.map(Number);
            filterObj = {
              ...filterObj,
              totalPrice: {
                $lte: endTotalPrice,
                $gte: startTotalPrice,
              },
            };
            break;
          case 'paymentMethod':
            filterObj = {
              ...filterObj,
              paymentMethod: Number(value),
            };
            break;
          case 'paymentMethods':
            filterObj = {
              ...filterObj,
              paymentMethod: { $in: value?.split(',')?.map(Number) },
            };
            break;
          case 'paymentStatus':
            filterObj = {
              ...filterObj,
              paymentStatus: Number(value),
            };
            break;
          case 'paymentStatuses':
            filterObj = {
              ...filterObj,
              paymentStatus: { $in: value?.split(',')?.map(Number) },
            };
            break;
          case 'status':
            filterObj = {
              ...filterObj,
              status: Number(value),
            };
            break;
          case 'statuses':
            filterObj = {
              ...filterObj,
              status: { $in: value?.split(',')?.map(Number) },
            };
            break;
          case 'shipperId':
            filterObj = {
              ...filterObj,
              shipper: new Types.ObjectId(value),
            };
            break;
          case 'shipperIds':
            filterObj = {
              ...filterObj,
              shipper: { $in: value?.split(',')?.map(Number) },
            };
            break;
          case 'productIds':
            const productIds = value
              ?.split(',')
              ?.map((id) => new Types.ObjectId(id));
            filterObj = {
              ...filterObj,
              'orderDetails.product': { $in: productIds },
            };
            break;
          case 'createdAt':
            const [startCreateAt, endCreateAt] = item.text.split('|');
            filterObj = {
              ...filterObj,
              createdAt: {
                $lte: moment(endCreateAt).endOf('day').toDate(),
                $gte: moment(startCreateAt).startOf('day').toDate(),
              },
            };
            break;
          case 'updatedAt':
            const [startUpdateAt, endUpdateAt] = item.text.split('|');
            filterObj = {
              ...filterObj,
              updatedAt: {
                $lte: moment(endUpdateAt).endOf('day').toDate(),
                $gte: moment(startUpdateAt).startOf('day').toDate(),
              },
            };
            break;
          default:
            break;
        }
      });
    }

    if (role === USER_ROLE_ENUM.USER) {
      filterObj = {
        ...filterObj,
        user: new Types.ObjectId(request.userId),
      };
    }

    if (role === USER_ROLE_ENUM.SHIPPER) {
      filterObj = {
        ...filterObj,
        shipper: new Types.ObjectId(request.userId),
      };
    }

    if (!isEmpty(sort)) {
      sort.forEach((item) => {
        const order = convertOrderMongo(item.order);
        switch (item.column) {
          case 'note':
            sortObj = { ...sortObj, note: order };
            break;
          case 'address':
            sortObj = { ...sortObj, address: order };
            break;
          case 'totalPrice':
            sortObj = { ...sortObj, totalPrice: order };
            break;
          case 'paymentMethod':
            sortObj = { ...sortObj, paymentMethod: order };
            break;
          case 'paymentStatus':
            sortObj = { ...sortObj, paymentStatus: order };
            break;
          case 'status':
            sortObj = { ...sortObj, status: order };
            break;
          case 'createdAt':
            sortObj = { ...sortObj, createdAt: order };
            break;
          case 'updatedAt':
            sortObj = { ...sortObj, updatedAt: order };
            break;
          default:
            break;
        }
      });
    } else {
      sortObj = { createdAt: SortOrder.DESC };
    }

    const [orders, total] = await Promise.all([
      this.orderModel
        .find(filterObj)
        .populate([
          {
            path: 'user',
            select: 'email fullname',
          },
          {
            path: 'shipper',
            select: 'email fullname phone',
          },
          {
            path: 'orderDetails.product',
            populate: {
              path: 'category',
              select: 'name code slug image description',
            },
          },
        ])
        .limit(take)
        .skip(skip)
        .sort(sortObj)
        .exec(),
      this.orderModel.countDocuments(filterObj).exec(),
    ]);

    return { data: orders, total };
  }

  async statisticByStatus(request: BaseDto): Promise<StatisticByStatusType[]> {
    const { userId } = request;

    const result = (await this.orderModel.aggregate([
      { $match: { user: new Types.ObjectId(userId) } },
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $project: { _id: 0, status: '$_id', count: 1 } },
    ])) as StatisticByStatusType[];

    return result;
  }

  async getSummaryOrders(request: GetSummaryOrderRequestDto): Promise<{
    summary: { status: number; count: number }[];
    start: Date;
    end: Date;
  }> {
    const { type } = request;
    const { start, end } = getStartAndEndTime(type);

    const summary = await this.orderModel.aggregate([
      { $match: { createdAt: { $gte: start, $lte: end } } },
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $project: { _id: 0, status: '$_id', count: 1 } },
    ]);

    return { summary, start, end };
  }
}
