import * as mongoose from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseModel } from '@core/schema/base.model';
import { PRODUCT_STATUS_ENUM } from '@components/product/product.constant';

@Schema({
  timestamps: true,
  collection: 'products',
  collation: { locale: 'vi' },
})
export class Product extends BaseModel {
  @Prop({
    type: String,
    required: true,
  })
  code: string;

  @Prop({
    type: String,
    required: true,
  })
  name: string;

  @Prop({
    type: String,
    required: true,
  })
  slug: string;

  @Prop({
    type: String,
    required: false,
  })
  description: string;

  @Prop({
    type: String,
    default: '',
  })
  image: string;

  @Prop({
    type: Number,
    required: true,
  })
  price: number;

  @Prop({
    type: Number,
    default: 0,
  })
  soldQuantity: number;

  @Prop({
    type: Number,
    enum: PRODUCT_STATUS_ENUM,
    default: PRODUCT_STATUS_ENUM.UPCOMING,
    index: true,
  })
  status: PRODUCT_STATUS_ENUM;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: true,
    index: true,
  })
  category: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false,
  })
  createdBy: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false,
  })
  deletedBy: string;
}

export const ProductSchema = SchemaFactory.createForClass(Product);

export type ProductDocument = Product & mongoose.Document;
