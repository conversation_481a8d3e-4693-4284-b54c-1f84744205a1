import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

import { BaseResponseDto } from '@core/dto/base.response.dto';
import { MessageDetailResponseDto } from './message-detail.response.dto';

class UserBaseResponseDto {
  @ApiProperty()
  @Expose()
  fullname: string;

  @ApiProperty()
  @Expose()
  email: string;

  @ApiProperty()
  @Expose()
  avatar: string;
}

export class ListLastChatForAdminResponseDto extends BaseResponseDto {
  @ApiProperty()
  @Expose()
  @Type(() => UserBaseResponseDto)
  user: UserBaseResponseDto;

  @ApiProperty()
  @Expose()
  @Type(() => MessageDetailResponseDto)
  chat: MessageDetailResponseDto;
}
