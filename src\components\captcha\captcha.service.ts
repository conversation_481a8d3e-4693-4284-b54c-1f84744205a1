import * as svgCaptcha from 'svg-captcha';
import { I18nService } from 'nestjs-i18n';
import { ConfigService } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cache, CACHE_MANAGER } from '@nestjs/cache-manager';

import { AllConfigType } from '@config/config.type';
import { ResponseBuilder } from '@utils/response-builder';
import { ResponsePayload } from '@utils/response-payload';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { encryptObj, expiresCheck } from '@helpers/crypto.helper';
import { VerifyCaptchaRequestDto } from '@components/captcha/dto/request/verify-captcha.request.dto';
import { GenerateCaptchaResponseDto } from '@components/captcha/dto/response/generate-captcha.response.dto';

@Injectable()
export class CaptchaService {
  private readonly logger = new Logger(CaptchaService.name);
  constructor(
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,

    private readonly i18n: I18nService,

    private configService: ConfigService<AllConfigType>,
  ) {}

  generate(): { sign: string; image: string } {
    const authConfig = this.configService.get('auth', {
      infer: true,
    });

    const { text, data: image } = svgCaptcha.create();
    // const base64 = `data:image/svg+xml;base64,${Buffer.from(image).toString('base64')}`;
    const expires = Date.now() + authConfig.captchaExpires;

    const sign = encryptObj(
      JSON.stringify({
        text,
        expires,
      }),
      authConfig.captchaSecret,
    );

    this.logger.log(`CAPTCHA TEXT: ${text}`);

    return { sign, image };
  }

  async verify(sign: string, text: string): Promise<boolean> {
    // kiểm tra xem captcha đã được sử dụng chưa
    const signUsed = await this.cacheManager.get(sign);

    if (signUsed) return false;

    const authConfig = this.configService.get('auth', {
      infer: true,
    });

    const { isExpired, payload } = expiresCheck(sign, authConfig.captchaSecret);

    if (text === payload?.text && !isExpired) {
      // nếu đúng captcha thì lưu lại vào cache để không sử dụng lại được nữa
      await this.cacheManager.set(sign, true, 30_000);
      return true;
    }

    return false;
  }

  buildCaptchaResponse(): ResponsePayload<GenerateCaptchaResponseDto> {
    const data = this.generate();

    const response = plainToInstance(GenerateCaptchaResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async verifyCaptcha(request: VerifyCaptchaRequestDto) {
    const { sign, text } = request;

    try {
      const isCorrect = await this.verify(sign, text);
      if (!isCorrect) {
        return new ResponseBuilder()
          .withCode(ResponseCodeEnum.BAD_REQUEST)
          .withMessage(this.i18n.translate('error.CAPTCHA_INVALID'))
          .build();
      }
    } catch (error) {
      this.logger.error(`VERIFY CAPTCHA ERROR: ${error}`);
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.BAD_REQUEST)
        .withMessage(this.i18n.translate('error.CAPTCHA_INVALID'))
        .build();
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }
}
