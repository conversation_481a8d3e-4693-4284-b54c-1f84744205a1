import { ApiProperty } from '@nestjs/swagger';

class UserLoginGoogleCallbackDto {
  @ApiProperty({
    description: 'The email of the user',
    example: 'kamil@mysliwiec',
    required: true,
  })
  email: string;

  @ApiProperty({
    description: 'The avatar of the user',
    example: 'https://example.com/avatar.jpg',
    required: true,
  })
  avatar: string;

  @ApiProperty({
    description: 'The fullname of the user',
    example: 'kamil mysliwiec',
    required: true,
  })
  fullname: string;
}

export class LoginGoogleCallbackRequestDto {
  user: UserLoginGoogleCallbackDto;
}
