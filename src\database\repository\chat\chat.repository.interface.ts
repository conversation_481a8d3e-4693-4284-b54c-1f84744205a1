import { PaginationQuery } from '@core/dto/pagination.query';
import { Chat as ChatModel } from '@database/schemas/chat.model';
import { BaseInterfaceRepository } from '@core/repository/base.interface.repository';
import { ListChatRequestDto } from '@components/chat/dto/request/list-chat.request.dto';
import { CreateChatRequestDto } from '@components/chat/dto/request/create-chat.request.dto';

export interface ChatRepositoryInterface
  extends BaseInterfaceRepository<ChatModel> {
  createEntity(data: CreateChatRequestDto): ChatModel;

  listForUser(
    request: ListChatRequestDto,
  ): Promise<{ data: ChatModel[]; total: number }>;

  listForAdmin(
    request: ListChatRequestDto,
    userId: string,
  ): Promise<{ data: ChatModel[]; total: number }>;

  listLastChatForAdmin(
    request: PaginationQuery,
  ): Promise<{ data: ChatModel[]; total: number }>;
}
