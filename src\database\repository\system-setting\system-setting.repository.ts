import { isEmpty } from 'lodash';
import { Model } from 'mongoose';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { SystemSetting } from '@database/schemas/system-setting.model';
import { BaseAbstractRepository } from '@core/repository/base.abstract.repository';
import { SystemSettingRepositoryInterface } from './system-setting.repository.interface';

@Injectable()
export class SystemSettingRepository
  extends BaseAbstractRepository<SystemSetting>
  implements SystemSettingRepositoryInterface
{
  constructor(
    @InjectModel(SystemSetting.name)
    private readonly systemSettingModel: Model<SystemSetting>,
  ) {
    super(systemSettingModel);
  }

  createEntity(data: {
    key: string;
    value: any;
    description?: string;
    createdBy?: string;
  }): SystemSetting {
    const entity = new this.systemSettingModel();
    entity.key = data.key;
    entity.value = data.value;
    entity.description = data.description;
    entity.createdBy = data.createdBy;

    return entity;
  }

  updateEntity(
    entity: SystemSetting,
    data: {
      value: any;
      updatedBy?: string;
      description?: string;
    },
  ): SystemSetting {
    entity.value = data.value;

    if (data.description) {
      entity.description = data.description;
    }

    if (data.updatedBy) {
      entity.updatedBy = data.updatedBy;
    }

    return entity;
  }

  async findByKey(key: string): Promise<SystemSetting | null> {
    return this.findOne({ key });
  }

  async getValueByKey(key: string): Promise<any | null> {
    const setting = await this.findByKey(key);
    return setting ? setting.value : null;
  }

  async setValueByKey(
    key: string,
    value: any,
    description?: string,
    updatedBy?: string,
  ): Promise<SystemSetting> {
    const existingSetting = await this.findByKey(key);

    if (isEmpty(existingSetting)) {
      const entity = this.createEntity({
        key,
        value,
        description,
        createdBy: updatedBy,
      });
      return await entity.save();
    } else {
      const updatedEntity = this.updateEntity(existingSetting, {
        value,
        updatedBy,
        description,
      });
      return await updatedEntity.save();
    }
  }
}
