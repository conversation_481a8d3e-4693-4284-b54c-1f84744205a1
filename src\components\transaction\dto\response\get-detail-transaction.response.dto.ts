import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

import { BaseResponseDto } from '@core/dto/base.response.dto';

class UserResponseDto {
  @ApiProperty()
  @Expose()
  fullname: string;

  @ApiProperty()
  @Expose()
  email: string;

  @ApiProperty()
  @Expose()
  phone: string;

  @ApiProperty()
  @Expose()
  role: number;

  @ApiProperty()
  @Expose()
  gender: number;

  @ApiProperty()
  @Expose()
  avatar: string;
}

export class GetDetailTransactionResponseDto extends BaseResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  amount: number;

  @ApiProperty()
  @Expose()
  balanceBefore: number;

  @ApiProperty()
  @Expose()
  balanceAfter: number;

  @ApiProperty()
  @Expose()
  action: number;

  @ApiProperty()
  @Expose()
  note: string;

  @ApiProperty()
  @Expose()
  @Type(() => UserResponseDto)
  user: UserResponseDto;
}
