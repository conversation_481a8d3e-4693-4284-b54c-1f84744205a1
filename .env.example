# Config app
PORT='9000'
NODE_ENV='development'
TZ='Asia/Ho_<PERSON>_<PERSON>'
FRONTEND_DOMAIN='http://localhost:3000'
SOCKET_URL='http://localhost:3000'

# Config database
DATABASE_TYPE='mongodb'
DATABASE_HOST='localhost'
DATABASE_PORT='27017'
DATABASE_NAME='base-nestjs'
DATABASE_USERNAME='admin'
DATABASE_PASSWORD='12345abc'
DATABASE_REJECT_UNAUTHORIZED='true'
# DATABASE_URL='mongodb://localhost:27017/'

REDIS_PORT=6379
REDIS_HOST='localhost'

# Auth config
AUTH_ACCESS_SECRET='secret'
AUTH_ACCESS_TOKEN_EXPIRES_IN='7d'
AUTH_REFRESH_SECRET='refresh'
AUTH_REFRESH_TOKEN_EXPIRES_IN='30d'
TWO_2FA_SECRET='2fa'
TWO_2FA_TOKEN_EXPIRES_IN='30s'
CAPTCHA_SECRET='captcha'
CAPTCHA_TOKEN_EXPIRES_IN=30 # seconds

# Cloudinary config
CLOUDINARY_CLOUD_NAME=''
CLOUDINARY_API_KEY=''
CLOUDINARY_API_SECRET=''

# Cloudflare config
CLOUDFLARE_TURNSTILE_SECRET_KEY=''

# R2 config
R2_BUCKET_NAME='hauifood'
R2_ACCESS_KEY=''
R2_SECRET_KEY=''
R2_ENDPOINT=''

# cloud provider cloudinary | s3
CLOUD_PROVIDER='s3'
DOMAIN_FILE='https://file.hauifood.com'

# 465 | 587 | 25
SEND_MAIL_HOST='smtp.gmail.com'
SEND_MAIL_PORT=465
SEND_MAIL_USERNAME=''
SEND_MAIL_PASSWORD=''
SEND_MAIL_NO_REPLY='<EMAIL>'
SEND_MAIL_TLS='SSLv3'

# Config google
GOOGLE_CLIENT_ID='your-client-id'
GOOGLE_CLIENT_SECRET='your-client-secret'

# VN Pay
VN_PAY_URL='https://sandbox.vnpayment.vn/paymentv2/vpcpay.html'
VN_PAY_TMN_CODE=''
VN_PAY_SECURE_SECRET=''
VN_PAY_RETURN_URL='http://localhost:8080/api/v1/payments/callback/vnpay'
VN_PAY_DEPOSIT_URL='http://localhost:8080/api/v1/transactions/callback/vnpay'

# Bot logs
TELEGRAM_CHAT_ID=''
TELEGRAM_LOGS_BOT_TOKEN=''

# Bot backup
TELEGRAM_BACKUP_BOT_TOKEN=''
TELEGRAM_BACKUP_CHAT_ID=''
BACKUP_ENCRYPTION_KEY=''

# Gemini API Key
GEMINI_API_KEY=''

# OpenAI API Key
OPENAI_API_KEY=''

# Momo
MOMO_ACCESS_KEY=''
MOMO_API_URL=''
MOMO_IPN_URL=''
MOMO_PARTNER_CODE=''
MOMO_PARTNER_NAME=''
MOMO_REDIRECT_URL=''
MOMO_SECRET_KEY=''
MOMO_STORE_ID=''

# Zalo
ZALO_API_URL=''
ZALO_APP_ID=''
ZALO_APP_USER=''
ZALO_CALLBACK_URL=''
ZALO_KEY1=''
ZALO_KEY2=''
ZALO_REDIRECT_URL=''
