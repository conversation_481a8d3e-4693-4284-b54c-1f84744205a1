import { lastValueFrom } from 'rxjs';
import { I18nService } from 'nestjs-i18n';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import { DeleteObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';

import {
  s3Client,
  R2_BUCKET_NAME,
} from '@components/cloudflare/cloudflare.provider';
import { AllConfigType } from '@config/config.type';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { BusinessException } from '@core/exception-filter/business-exception.filter';

@Injectable()
export class CloudflareService {
  private readonly logger = new Logger(CloudflareService.name);

  private readonly verifyUrl =
    'https://challenges.cloudflare.com/turnstile/v0/siteverify';

  constructor(
    private readonly i18n: I18nService,

    private readonly httpService: HttpService,

    private configService: ConfigService<AllConfigType>,
  ) {}

  /**
   * <PERSON><PERSON>c thực token captcha Cloudflare Turnstile
   * @param token Token từ client-side Turnstile widget
   * @param remoteip Địa chỉ IP của người dùng (tùy chọn)
   * @returns Promise với kết quả xác thực
   */
  async verifyCaptcha(
    token: string,
    remoteip?: string,
  ): Promise<{
    success: boolean;
    challenge_ts?: string;
    hostname?: string;
    error_codes?: string[];
    action?: string;
    cdata?: string;
  }> {
    const cloudflare = this.configService.get('cloudflare', {
      infer: true,
    });

    const formData = new URLSearchParams();
    formData.append('response', token);
    formData.append('secret', cloudflare.turnstileSecretKey);

    if (remoteip) {
      formData.append('remoteip', remoteip);
    }

    try {
      const response = await lastValueFrom(
        this.httpService.post(this.verifyUrl, formData, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }),
      );

      return response.data;
    } catch (error) {
      this.logger.error(`[VERIFY CAPTCHA ERROR]: ${error}`);
      throw new BusinessException(
        this.i18n.translate('error.VALIDATE_CAPTCHA_FAILED'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }
  }

  async uploadToR2(file: Express.Multer.File): Promise<{ fileUrl: string }> {
    const fileUrl = `uploads/${Date.now()}-${file.originalname}`;

    const uploadParams = {
      Key: fileUrl,
      Body: file.buffer,
      Bucket: R2_BUCKET_NAME,
      ContentType: file.mimetype,
    };

    try {
      const result = await s3Client.send(new PutObjectCommand(uploadParams));
      if (result.$metadata.httpStatusCode !== 200) {
        throw new BusinessException(
          this.i18n.translate('error.UPLOAD_FAILED'),
          ResponseCodeEnum.BAD_REQUEST,
        );
      }

      return { fileUrl };
    } catch (error) {
      this.logger.error(`[UPLOAD TO R2 ERROR]: ${error}`);
      throw new BusinessException(
        this.i18n.translate('error.UPLOAD_FAILED'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }
  }

  async deleteFromR2(fileKey: string): Promise<void> {
    const deleteParams = {
      Key: fileKey,
      Bucket: R2_BUCKET_NAME,
    };
    await s3Client.send(new DeleteObjectCommand(deleteParams));
  }
}
