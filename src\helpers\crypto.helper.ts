import * as CryptoJS from 'crypto-js';

export function objectToString(obj: any): string {
  return JSON.stringify(obj);
}

export function stringToObject(str: string): any {
  return JSON.parse(str);
}

export function encrypt(plainText: string, secret: string): string {
  return CryptoJS.AES.encrypt(plainText, secret).toString();
}

export function decrypt(cipherText: string, secret: string): string {
  const bytes = CryptoJS.AES.decrypt(cipherText, secret);
  return bytes.toString(CryptoJS.enc.Utf8);
}

export function encryptObj(obj: any, secret: string): string {
  return encrypt(objectToString(obj), secret);
}

export function decryptObj(cipherText: string, secret: string): any | null {
  try {
    return stringToObject(decrypt(cipherText, secret));
  } catch (error) {
    return null;
  }
}

export const expiresCheck = (
  token: string,
  secret: string,
  timeDiff = 0,
): { isExpired: boolean; payload: any } => {
  const payload = decryptObj(token, secret);

  const payloadObj = stringToObject(payload);

  const isExpired = Date.now() + timeDiff > payloadObj?.expires;

  return { isExpired, payload: payloadObj };
};
