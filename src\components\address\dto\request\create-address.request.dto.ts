import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>NotEmpty,
  IsO<PERSON>al,
  IsPhoneNumber,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import {
  ADDRESS_CONST,
  IS_DEFAULT_ADDRESS_ENUM,
} from '@components/address/address.constant';
import { BaseDto } from '@core/dto/base.request.dto';

export class CreateAddressRequestDto extends BaseDto {
  @ApiProperty({
    description: 'Tên người nhận',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(ADDRESS_CONST.RECIPIENT_NAME.MIN_LENGTH)
  @MaxLength(ADDRESS_CONST.RECIPIENT_NAME.MAX_LENGTH)
  recipientName: string;

  @ApiProperty({
    description: 'Số điện thoại',
    example: '0901234567',
  })
  @IsString()
  @IsNotEmpty()
  @IsPhoneNumber('VN')
  phone: string;

  @ApiProperty({
    description: 'Địa chỉ',
    example: '123 <PERSON><PERSON><PERSON><PERSON>, Q.1, TP.HCM',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(ADDRESS_CONST.ADDRESS.MIN_LENGTH)
  @MaxLength(ADDRESS_CONST.ADDRESS.MAX_LENGTH)
  address: string;

  @ApiProperty({
    description: 'Ghi chú (không bắt buộc)',
    example: 'Ghi chú về địa chỉ',
  })
  @IsString()
  @MaxLength(ADDRESS_CONST.NOTE.MAX_LENGTH)
  @IsOptional()
  note: string;

  @ApiProperty({
    description: 'Địa chỉ mặc định (không bắt buộc)',
    example: 1,
    enum: IS_DEFAULT_ADDRESS_ENUM,
  })
  @IsEnum(IS_DEFAULT_ADDRESS_ENUM)
  @IsOptional()
  isDefault: IS_DEFAULT_ADDRESS_ENUM;
}
