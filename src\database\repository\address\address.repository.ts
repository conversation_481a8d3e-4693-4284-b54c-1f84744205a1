import * as moment from 'moment';
import { isEmpty } from 'lodash';
import { Model, Types } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

import { Address } from '@database/schemas/address.model';
import { AddressRepositoryInterface } from './address.repository.interface';
import { convertOrderMongo, getRegexByValue, SortOrder } from '@utils/common';
import { IS_DEFAULT_ADDRESS_ENUM } from '@components/address/address.constant';
import { BaseAbstractRepository } from '@core/repository/base.abstract.repository';
import { GetMyAddressRequestDto } from '@components/address/dto/request/get-my-address.request.dto';
import { UpdateAddressRequestDto } from '@components/address/dto/request/update-address.request.dto';
import { CreateAddressRequestDto } from '@components/address/dto/request/create-address.request.dto';

export class AddressRepository
  extends BaseAbstractRepository<Address>
  implements AddressRepositoryInterface
{
  constructor(
    @InjectModel('Address')
    private readonly addressModel: Model<Address>,
  ) {
    super(addressModel);
  }

  createEntity(data: CreateAddressRequestDto): Address {
    const entity = new this.addressModel();

    entity.note = data.note;
    entity.user = data.userId;
    entity.phone = data.phone;
    entity.address = data.address;
    entity.recipientName = data.recipientName;
    entity.isDefault = data.isDefault ?? IS_DEFAULT_ADDRESS_ENUM.NO;

    return entity;
  }

  updateEntity(entity: Address, data: UpdateAddressRequestDto): Address {
    entity.note = data.note;
    entity.phone = data.phone;
    entity.address = data.address;
    entity.recipientName = data.recipientName;
    entity.isDefault = data.isDefault ?? entity.isDefault;

    return entity;
  }

  async list(
    request: GetMyAddressRequestDto,
  ): Promise<{ data: Address[]; total: number }> {
    const { keyword, sort, filter, take, skip, userId } = request;

    let filterObj: any = { user: new Types.ObjectId(userId) };
    let sortObj: any = {
      isDefault: SortOrder.DESC,
      createdAt: SortOrder.DESC,
    };

    if (!isEmpty(keyword)) {
      const filterByKeyword = getRegexByValue(keyword);
      filterObj = {
        $or: [
          { phone: filterByKeyword },
          { address: filterByKeyword },
          { recipientName: filterByKeyword },
        ],
      };
    }

    if (!isEmpty(filter)) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item.column) {
          case 'recipientName':
            filterObj = {
              ...filterObj,
              recipientName: getRegexByValue(value),
            };
            break;
          case 'phone':
            filterObj = {
              ...filterObj,
              phone: getRegexByValue(value),
            };
            break;
          case 'address':
            filterObj = {
              ...filterObj,
              address: getRegexByValue(value),
            };
            break;
          case 'note':
            filterObj = {
              ...filterObj,
              note: getRegexByValue(value),
            };
            break;
          case 'isDefault':
            filterObj = {
              ...filterObj,
              isDefault: Number(value),
            };
            break;
          case 'createdAt':
            const [startCreateAt, endCreateAt] = item.text.split('|');
            filterObj = {
              ...filterObj,
              createdAt: {
                $lte: moment(endCreateAt).endOf('day').toDate(),
                $gte: moment(startCreateAt).startOf('day').toDate(),
              },
            };
            break;
          case 'updatedAt':
            const [startUpdateAt, endUpdateAt] = item.text.split('|');
            filterObj = {
              ...filterObj,
              updatedAt: {
                $lte: moment(endUpdateAt).endOf('day').toDate(),
                $gte: moment(startUpdateAt).startOf('day').toDate(),
              },
            };
            break;
          default:
            break;
        }
      });
    }

    if (!isEmpty(sort)) {
      sort.forEach((item) => {
        const order = convertOrderMongo(item.order);
        switch (item.column) {
          case 'recipientName':
            sortObj = { ...sortObj, recipientName: order };
            break;
          case 'phone':
            sortObj = { ...sortObj, phone: order };
            break;
          case 'address':
            sortObj = { ...sortObj, address: order };
            break;
          case 'note':
            sortObj = { ...sortObj, note: order };
            break;
          case 'isDefault':
            sortObj = { ...sortObj, isDefault: order };
            break;
          case 'createdAt':
            sortObj = { ...sortObj, createdAt: order };
            break;
          case 'updatedAt':
            sortObj = { ...sortObj, updatedAt: order };
            break;
          default:
            break;
        }
      });
    }
    const [addresses, total] = await Promise.all([
      this.addressModel
        .find(filterObj)
        .limit(take)
        .skip(skip)
        .sort(sortObj)
        .exec(),
      this.addressModel.countDocuments(filterObj).exec(),
    ]);

    return { data: addresses, total };
  }
}
