import * as mongoose from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseModel } from '@core/schema/base.model';
import { IS_DEFAULT_ADDRESS_ENUM } from '@components/address/address.constant';

@Schema({
  timestamps: true,
  collection: 'addresses',
  collation: { locale: 'vi' },
})
export class Address extends BaseModel {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false,
    index: true,
  })
  user: string;

  @Prop({
    type: String,
    required: true,
  })
  recipientName: string;

  @Prop({
    type: String,
    required: true,
  })
  phone: string;

  @Prop({
    type: String,
    required: true,
  })
  address: string;

  @Prop({
    type: String,
    required: false,
  })
  note: string;

  @Prop({
    type: Number,
    enum: IS_DEFAULT_ADDRESS_ENUM,
    default: IS_DEFAULT_ADDRESS_ENUM.NO,
  })
  isDefault: IS_DEFAULT_ADDRESS_ENUM;
}

export const AddressSchema = SchemaFactory.createForClass(Address);

export type AddressDocument = Address & mongoose.Document;
