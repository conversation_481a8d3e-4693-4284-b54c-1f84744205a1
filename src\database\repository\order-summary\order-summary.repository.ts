import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

import { getStartAndEndTime } from '@helpers/date.helper';
import { OrderSummary } from '@database/schemas/order-summary.model';
import { BaseAbstractRepository } from '@core/repository/base.abstract.repository';
import { TOP_SELLING_PRODUCT_LIMIT } from '@components/dashboard/dashboard.constant';
import { OrderSummaryRepositoryInterface } from './order-summary.repository.interface';
import { GetTopSellingProductRequestDto } from '@components/dashboard/dto/request/get-top-selling-product.request.dto';

export class OrderSummaryRepository
  extends BaseAbstractRepository<OrderSummary>
  implements OrderSummaryRepositoryInterface
{
  constructor(
    @InjectModel('OrderSummary')
    private readonly orderSummaryModel: Model<OrderSummary>,
  ) {
    super(orderSummaryModel);
  }

  async getTopSellingProducts(request: GetTopSellingProductRequestDto) {
    const { type } = request;
    const { start, end } = getStartAndEndTime(type);

    const summary = await this.orderSummaryModel.aggregate([
      { $match: { date: { $gte: start, $lte: end } } },
      {
        $group: {
          _id: '$product',
          totalPrice: { $sum: '$totalPrice' },
          totalQuantity: { $sum: '$quantity' },
        },
      },
      { $sort: { totalQuantity: -1 } },
      { $limit: TOP_SELLING_PRODUCT_LIMIT },
    ]);

    return { summary, start, end };
  }
}
