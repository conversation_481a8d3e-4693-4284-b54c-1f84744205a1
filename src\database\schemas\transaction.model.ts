import * as mongoose from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseModel } from '@core/schema/base.model';
import { TRANSACTION_ACTION_ENUM } from '@components/transaction/transaction.constant';

@Schema({
  timestamps: true,
  collection: 'transactions',
  collation: { locale: 'vi' },
})
export class Transaction extends BaseModel {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true,
    required: true,
  })
  user: string;

  @Prop({
    required: true,
    type: String,
    index: true,
  })
  code: string;

  @Prop({
    type: Number,
    required: true,
  })
  amount: number;

  @Prop({
    required: true,
    type: Number,
  })
  balanceBefore: number;

  @Prop({
    required: true,
    type: Number,
  })
  balanceAfter: number;

  @Prop({
    required: true,
    enum: TRANSACTION_ACTION_ENUM,
  })
  action: TRANSACTION_ACTION_ENUM;

  @Prop({
    required: false,
    type: String,
  })
  note: string;
}

export const TransactionSchema = SchemaFactory.createForClass(Transaction);

export type TransactionDocument = Transaction & mongoose.Document;
