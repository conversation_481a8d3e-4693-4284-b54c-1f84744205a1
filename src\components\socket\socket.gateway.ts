import {
  MessageBody,
  OnGatewayInit,
  WebSocketServer,
  ConnectedSocket,
  WebSocketGateway,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { JwtService } from '@nestjs/jwt';
import { Server, Socket } from 'socket.io';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Inject, Injectable, Logger } from '@nestjs/common';

import { EVENT_ENUM } from '@constant/event.enum';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { CreateChatRequestDto } from '@components/chat/dto/request/create-chat.request.dto';
import { UserRepositoryInterface } from '@database/repository/user/user.repository.interface';

interface ConnectedUser {
  userId: string;
  socketId: string;
  isAdmin: boolean;
  fullname: string;
  email: string;
  avatar?: string;
}

@WebSocketGateway({
  cors: {
    origin: '*', // Allow all origins for testing
  },
  namespace: 'chat',
  transports: ['websocket', 'polling'],
  pingTimeout: 5000,
  pingInterval: 10000,
})
@Injectable()
export class SocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  public server: Server;

  private logger: Logger = new Logger(SocketGateway.name);

  private onlineUsers: Map<string, ConnectedUser> = new Map(); // Map userId -> user

  private connectedUsers: Map<string, ConnectedUser> = new Map(); // Map socketId -> user

  constructor(
    private readonly jwtService: JwtService,

    private readonly eventEmitter: EventEmitter2,

    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,
  ) {}

  afterInit(server: Server): void {
    this.server = server;
    this.logger.log('Initialized socket!');
  }

  async handleConnection(socket: Socket): Promise<void> {
    try {
      if (!socket || !socket.handshake) {
        this.logger.error('Lỗi: Socket không hợp lệ');
        if (socket) socket.disconnect();
        return;
      }

      const token = socket.handshake.auth?.token?.replaceAll('"', '');
      if (!token) {
        this.logger.error('Lỗi: Token không tồn tại');
        socket.disconnect();
        return;
      }

      try {
        // Giải mã token để lấy thông tin người dùng
        const decoded = await this.jwtService.verifyAsync(token);
        this.logger.log('Token giải mã thành công:', JSON.stringify(decoded));

        if (!decoded) {
          this.logger.error('Lỗi: Token không hợp lệ');
          socket.disconnect();
          return;
        }

        const userId = decoded.id || decoded.sub;
        if (!userId) {
          this.logger.error('Lỗi: Token không chứa ID người dùng');
          socket.disconnect();
          return;
        }

        const userInfo = await this.userRepository.findById(userId);
        if (!userInfo) {
          this.logger.error('Lỗi: Không tìm thấy thông tin người dùng');
          socket.disconnect();
          return;
        }

        // Tạo đối tượng user từ thông tin trong token
        const user: ConnectedUser = {
          userId: userId,
          socketId: socket.id,
          email: userInfo?.email || '',
          avatar: userInfo?.avatar || '',
          fullname: userInfo?.fullname || 'Người dùng',
          isAdmin: userInfo?.role === USER_ROLE_ENUM.ADMIN,
        };

        // Lưu thông tin kết nối
        this.connectedUsers.set(socket.id, user);
        this.onlineUsers.set(user.userId, user);

        this.logger.log(
          `User đã kết nối: ${user.fullname} (${user.userId}) - ${user.isAdmin ? 'Admin' : 'User'}`,
        );

        // Join vào room tương ứng
        socket.join(`user_${user.userId}`);
        if (user.isAdmin) {
          socket.join('admin_room');
        }

        // Broadcast danh sách user đang online cho các admin
        this.broadcastOnlineUsers();
      } catch (tokenError) {
        this.logger.error('Lỗi giải mã token:', tokenError.message);
        socket.emit('error', { message: 'Token không hợp lệ' });
        socket.disconnect();
      }
    } catch (error) {
      this.logger.error('Lỗi kết nối:', error.message);
      if (socket && socket.connected) socket.disconnect();
    }
  }

  handleDisconnect(socket: Socket): void {
    try {
      if (!socket || !socket.id) return;

      const user = this.connectedUsers.get(socket.id);
      if (user) {
        // Xóa user khỏi danh sách online
        this.connectedUsers.delete(socket.id);
        this.onlineUsers.delete(user.userId);

        this.logger.log(
          `User đã ngắt kết nối: ${user.fullname} (${user.userId})`,
        );

        // Broadcast danh sách user online mới cho admin
        this.broadcastOnlineUsers();
      }
    } catch (error) {
      this.logger.error('Lỗi xử lý ngắt kết nối:', error.message);
    }
  }

  // Gửi danh sách user đang online cho tất cả admin
  private broadcastOnlineUsers(): void {
    try {
      if (!this.server) {
        this.logger.error('Server socket chưa được khởi tạo');
        return;
      }

      const onlineUsersList = Array.from(this.onlineUsers.values())
        .filter((user) => !user.isAdmin) // Chỉ gửi danh sách user thường, không gửi admin
        .map((user) => ({
          userId: user.userId,
          fullname: user.fullname,
          email: user.email,
          avatar: user.avatar,
        }));

      this.server.to('admin_room').emit('onlineUsers', onlineUsersList);
    } catch (error) {
      this.logger.error('Lỗi broadcast danh sách users:', error.message);
    }
  }

  @SubscribeMessage('sendMessage')
  async handleMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { receiverId: string; message: string },
  ): Promise<any> {
    try {
      this.logger.log(
        `Nhận tin nhắn từ client: ${client?.id}, data: ${JSON.stringify(data)}`,
      );

      if (!client || !client.id) {
        this.logger.error(
          `Socket không hợp lệ hoặc không có ID: ${JSON.stringify(client)}`,
        );
        return { error: 'Socket không hợp lệ' };
      }

      if (!data) {
        this.logger.error('Dữ liệu tin nhắn không hợp lệ');
        if (client.connected)
          client.emit('error', { message: 'Dữ liệu không hợp lệ' });
        return { error: 'Dữ liệu không hợp lệ' };
      }

      const sender = this.connectedUsers.get(client.id);
      this.logger.log(
        `Tìm người gửi với socketId ${client.id}: ${JSON.stringify(sender)}`,
      );

      if (!sender || !sender.userId) {
        this.logger.error(
          `Không tìm thấy thông tin người gửi cho socket ID: ${client.id}`,
        );
        if (client.connected)
          client.emit('error', {
            message: 'Không tìm thấy thông tin người gửi',
          });
        return { error: 'Không tìm thấy thông tin người gửi' };
      }

      if (!data.message || data.message.trim() === '') {
        if (client.connected)
          client.emit('error', { message: 'Tin nhắn không được để trống' });
        return { error: 'Tin nhắn không được để trống' };
      }

      // Xác định người nhận
      let receiverId = data.receiverId;

      // Trường hợp người gửi là user thì gửi cho admin
      if (!sender.isAdmin && (!receiverId || receiverId === 'admin')) {
        // Message from user to admin
        receiverId = 'admin';
      }
      // Trường hợp người gửi là admin thì phải chỉ định receiverId cụ thể
      else if (sender.isAdmin && !receiverId) {
        if (client.connected)
          client.emit('error', { message: 'Hãy chọn người nhận' });
        return { error: 'Hãy chọn người nhận' };
      }

      this.logger.log(
        `Tin nhắn từ ${sender.fullname} (${sender.userId}) tới ${receiverId}: ${data.message}`,
      );

      // Tạo đối tượng tin nhắn
      const message = {
        senderId: sender.userId,
        senderName: sender.fullname,
        receiverId: receiverId,
        message: data.message,
        timestamp: new Date(),
        isAdmin: sender.isAdmin,
      };

      // Lưu tin nhắn vào cơ sở dữ liệu
      try {
        await this.saveChatMessage(sender, receiverId, data.message);
        this.logger.log(`Đã lưu tin nhắn vào database`);
      } catch (dbError) {
        this.logger.error(`Lỗi lưu tin nhắn: ${dbError.message}`);
        // Vẫn tiếp tục gửi tin nhắn dù có lỗi khi lưu
      }

      // Gửi tin nhắn theo từng trường hợp
      if (!this.server) {
        this.logger.error('Server socket chưa được khởi tạo');
        return { error: 'Server socket chưa được khởi tạo' };
      }

      try {
        if (sender.isAdmin) {
          // Admin gửi tin nhắn cho user cụ thể
          this.logger.log(`Gửi tin nhắn đến user: user_${receiverId}`);
          this.server.to(`user_${receiverId}`).emit('newMessage', message);
        } else {
          // User gửi tin nhắn cho tất cả admin
          this.logger.log(`Gửi tin nhắn đến tất cả admin trong admin_room`);
          this.server.to('admin_room').emit('newMessage', message);
        }

        // Gửi phản hồi cho người gửi
        if (client.connected) {
          this.logger.log(`Gửi xác nhận tin nhắn đến người gửi: ${client.id}`);
          client.emit('messageSent', message);
        }

        // Trả về kết quả thành công
        return { success: true, message };
      } catch (socketError) {
        this.logger.error(`Lỗi gửi tin nhắn socket: ${socketError.message}`);
        return { error: 'Lỗi gửi tin nhắn' };
      }
    } catch (error) {
      this.logger.error(
        `Lỗi xử lý tin nhắn: ${error.message}, stack: ${error.stack}`,
      );
      if (client && client.connected) {
        try {
          client.emit('error', { message: 'Lỗi khi gửi tin nhắn' });
        } catch (emitError) {
          this.logger.error(`Lỗi emit error: ${emitError.message}`);
        }
      }
      return { error: 'Lỗi xử lý tin nhắn' };
    }
  }

  // API để client lấy danh sách user đang online
  @SubscribeMessage('getOnlineUsers')
  handleGetOnlineUsers(socket: Socket): void {
    try {
      if (!socket || !socket.id) return;

      const user = this.connectedUsers.get(socket.id);
      if (!user || !user.isAdmin) return;

      const onlineUsersList = Array.from(this.onlineUsers.values())
        .filter((u) => !u.isAdmin)
        .map((u) => ({
          userId: u.userId,
          fullname: u.fullname,
          email: u.email,
          avatar: u.avatar,
        }));

      if (socket.connected) {
        socket.emit('onlineUsers', onlineUsersList);
      }
    } catch (error) {
      this.logger.error(`Lỗi lấy danh sách user online: ${error.message}`);
    }
  }

  // Lưu tin nhắn vào database
  private async saveChatMessage(
    sender: ConnectedUser,
    receiverId: string,
    content: string,
  ) {
    try {
      if (!sender || !sender.userId || !receiverId) {
        throw new Error('Thông tin người gửi không hợp lệ');
      }

      const createChatDto = new CreateChatRequestDto();

      createChatDto.message = content;
      createChatDto.receiver = receiverId; // người nhận
      createChatDto.userId = sender?.userId; // người gửi
      createChatDto.sender = sender?.userId; // người gửi

      this.eventEmitter.emit(EVENT_ENUM.SAVE_MESSAGE, createChatDto);
    } catch (error) {
      this.logger.error(`Lỗi lưu tin nhắn vào database: ${error.message}`);
      throw error;
    }
  }
}
