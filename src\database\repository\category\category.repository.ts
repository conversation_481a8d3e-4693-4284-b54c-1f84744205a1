import * as moment from 'moment';
import { isEmpty } from 'lodash';
import { Model } from 'mongoose';
import { PipelineStage } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

import { Category } from '@database/schemas/category.model';
import { convertOrderMongo, getRegexByValue, SortOrder } from '@utils/common';
import { CategoryRepositoryInterface } from './category.repository.interface';
import { BaseAbstractRepository } from '@core/repository/base.abstract.repository';
import { UpdateCategoryRequestDto } from '@components/category/dto/request/update-category.request.dto';
import { CreateCategoryRequestDto } from '@components/category/dto/request/create-category.request.dto';
import { GetListCategoryRequestDto } from '@components/category/dto/request/get-list-category.request.dto';

export class CategoryRepository
  extends BaseAbstractRepository<Category>
  implements CategoryRepositoryInterface
{
  constructor(
    @InjectModel('Category')
    private readonly categoryModel: Model<Category>,
  ) {
    super(categoryModel);
  }

  createEntity(data: CreateCategoryRequestDto): Category {
    const entity = new this.categoryModel();

    entity.name = data.name;
    entity.code = data.code;
    entity.slug = data.slug;
    entity.image = data.image;
    entity.description = data.description;

    entity.createdBy = data.userId;

    return entity;
  }

  updateEntity(entity: Category, data: UpdateCategoryRequestDto): Category {
    entity.name = data.name;
    entity.slug = data.slug;
    entity.image = data.image;
    entity.description = data.description;

    return entity;
  }

  async getDetail(id: string): Promise<Category | null> {
    return await this.categoryModel
      .findOne({ _id: id, deletedAt: null })
      .exec();
  }

  async list(
    request: GetListCategoryRequestDto,
    isExport = false,
  ): Promise<{ data: Category[]; total: number }> {
    const { keyword, sort, filter, page = 1, limit = 20 } = request;

    const take = limit;
    const skip = (page - 1) * limit;

    let filterObj: any = {};
    let sortObj: any = { createdAt: SortOrder.DESC };

    if (!isEmpty(keyword)) {
      const filterByKeyword = getRegexByValue(keyword);
      filterObj = {
        $or: [
          { name: filterByKeyword },
          { slug: filterByKeyword },
          { code: filterByKeyword },
        ],
      };
    }

    if (!isEmpty(filter)) {
      filter.forEach((item) => {
        const value = item ? item.text : null;
        switch (item.column) {
          case 'name':
            filterObj = {
              ...filterObj,
              name: getRegexByValue(value),
            };
            break;
          case 'code':
            filterObj = {
              ...filterObj,
              code: getRegexByValue(value),
            };
            break;
          case 'slug':
            filterObj = {
              ...filterObj,
              slug: getRegexByValue(value),
            };
            break;
          case 'createdAt':
            const [startCreateAt, endCreateAt] = item.text.split('|');
            filterObj = {
              ...filterObj,
              createdAt: {
                $lte: moment(endCreateAt).endOf('day').toDate(),
                $gte: moment(startCreateAt).startOf('day').toDate(),
              },
            };
            break;
          case 'updatedAt':
            const [startUpdateAt, endUpdateAt] = item.text.split('|');
            filterObj = {
              ...filterObj,
              updatedAt: {
                $lte: moment(endUpdateAt).endOf('day').toDate(),
                $gte: moment(startUpdateAt).startOf('day').toDate(),
              },
            };
            break;
          default:
            break;
        }
      });
    }

    if (!isEmpty(sort)) {
      sort.forEach((item) => {
        const order = convertOrderMongo(item.order);
        switch (item.column) {
          case 'name':
            sortObj = { ...sortObj, name: order };
            break;
          case 'code':
            sortObj = { ...sortObj, code: order };
            break;
          case 'slug':
            sortObj = { ...sortObj, slug: order };
            break;
          case 'createdAt':
            sortObj = { ...sortObj, createdAt: order };
            break;
          case 'updatedAt':
            sortObj = { ...sortObj, updatedAt: order };
            break;
          default:
            break;
        }
      });
    }

    const pipeline: any[] = [
      { $match: { deletedAt: null, ...filterObj } },
      { $sort: sortObj },
    ];

    if (!isExport) {
      pipeline.push(
        { $skip: skip },
        { $limit: take },
        {
          $lookup: {
            from: 'products',
            localField: '_id',
            foreignField: 'category',
            as: 'products',
          },
        },
        {
          $addFields: {
            productCount: { $size: '$products' },
          },
        },
        {
          $project: {
            products: 0,
          },
        },
      );
    }

    const [categories, total] = await Promise.all([
      this.categoryModel.aggregate(pipeline).exec(),
      !isExport
        ? this.categoryModel
            .countDocuments({ deletedAt: null, ...filterObj })
            .exec()
        : 0,
    ]);

    return { data: categories, total };
  }

  async findPopular(): Promise<Category[]> {
    const pipeline: PipelineStage[] = [
      {
        $match: {
          deletedAt: null,
        },
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: 'category',
          as: 'products',
        },
      },
      {
        $addFields: {
          productCount: { $size: '$products' },
          totalSold: {
            $reduce: {
              input: '$products',
              initialValue: 0,
              in: {
                $add: ['$$value', { $ifNull: ['$$this.soldQuantity', 0] }],
              },
            },
          },
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          code: 1,
          slug: 1,
          image: 1,
          description: 1,
          productCount: 1,
          totalSold: 1,
          createdAt: 1,
          updatedAt: 1,
        },
      },
      {
        $sort: {
          totalSold: -1,
          productCount: -1,
        },
      },
      {
        $limit: 5,
      },
    ];

    return await this.categoryModel.aggregate(pipeline);
  }

  async findByKeywords(keywords: string[]): Promise<Category[]> {
    const keywordConditions = keywords.map((keyword) => ({
      $or: [
        { name: getRegexByValue(keyword) },
        { code: getRegexByValue(keyword) },
      ],
    }));

    const pipeline: PipelineStage[] = [
      {
        $match: {
          deletedAt: null,
          $or: keywordConditions,
        },
      },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: 'category',
          as: 'products',
        },
      },
      {
        $addFields: {
          productCount: { $size: '$products' },
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          code: 1,
          slug: 1,
          image: 1,
          description: 1,
          productCount: 1,
          createdAt: 1,
          updatedAt: 1,
        },
      },
      {
        $sort: {
          productCount: -1,
        },
      },
      {
        $limit: 10,
      },
    ];

    return await this.categoryModel.aggregate(pipeline);
  }
}
