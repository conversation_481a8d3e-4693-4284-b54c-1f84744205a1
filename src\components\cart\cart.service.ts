import { isEmpty } from 'lodash';
import { ObjectId } from 'mongodb';
import { I18nService } from 'nestjs-i18n';
import { OnEvent } from '@nestjs/event-emitter';
import { plainToInstance } from 'class-transformer';
import { Inject, Injectable } from '@nestjs/common';

import { CART_CONST } from './cart.constant';
import { EVENT_ENUM } from '@constant/event.enum';
import { User } from '@database/schemas/user.model';
import { ResponseBuilder } from '@utils/response-builder';
import { Product } from '@database/schemas/product.model';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { MyCartResponseDto } from './dto/response/my-cart.response.dto';
import { PRODUCT_STATUS_ENUM } from '@components/product/product.constant';
import { BusinessException } from '@core/exception-filter/business-exception.filter';
import { AddProductToCartRequestDto } from './dto/request/add-product-to-cart.request.dto';
import { CartRepositoryInterface } from '@database/repository/cart/cart.repository.interface';
import { RemoveProductToCartRequestDto } from './dto/request/remove-product-to-cart.request.dto';
import { ProductRepositoryInterface } from '@database/repository/product/product.repository.interface';
import { RemoveFastProductToCartRequestDto } from './dto/request/remove-fast-product-to-cart.request.dto';

@Injectable()
export class CartService {
  constructor(
    private readonly i18n: I18nService,

    @Inject('CartRepositoryInterface')
    private readonly cartRepository: CartRepositoryInterface,

    @Inject('ProductRepositoryInterface')
    private readonly productRepository: ProductRepositoryInterface,
  ) {}

  @OnEvent(EVENT_ENUM.INIT_CART)
  async init(user: User) {
    const { id } = user;

    const cartExist = await this.cartRepository.findOne({ user: id });
    if (isEmpty(cartExist)) {
      await this.cartRepository.create({ user: id });
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.CREATED)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async myCart(user: User) {
    const { id } = user;

    const cart = await this.cartRepository
      .findOne({ user: id })
      .populate<{
        cartDetails: Array<{ product: Product; quantity: number }>;
      }>({
        path: 'cartDetails.product',
        select: 'image name price status description',
      })
      .lean();

    if (isEmpty(cart)) {
      throw new BusinessException(
        this.i18n.translate('error.CART_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const cartWithDetails = {
      ...cart,
      totalQuantity: cart.cartDetails.reduce(
        (total, item) => total + item.quantity,
        0,
      ),
      totalPrice: cart.cartDetails.reduce(
        (total, item) => total + item.product.price * item.quantity,
        0,
      ),
      cartDetails: cart.cartDetails.map((item) => ({
        ...item,
        subtotal: item.product.price * item.quantity,
      })),
    };

    const response = plainToInstance(MyCartResponseDto, cartWithDetails, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async addProduct(request: AddProductToCartRequestDto) {
    const { productId, quantity, userId } = request;

    const product = await this.productRepository.getDetail(productId);
    if (isEmpty(product)) {
      throw new BusinessException(
        this.i18n.translate('error.PRODUCT_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    if (product.status !== PRODUCT_STATUS_ENUM.ACTIVE) {
      throw new BusinessException(
        this.i18n.translate('error.PRODUCT_STATUS_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const cart = await this.cartRepository
      .findOne({ user: userId })
      .populate<{
        cartDetails: Array<{ product: Product; quantity: number }>;
      }>({
        path: 'cartDetails.product',
      })
      .lean();

    if (isEmpty(cart)) {
      throw new BusinessException(
        this.i18n.translate('error.CART_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const exitsProduct = cart.cartDetails.find((item) => {
      return item.product._id.toString() === productId.toString();
    });

    if (!isEmpty(exitsProduct)) {
      const invalidQuantity =
        exitsProduct.quantity + quantity > CART_CONST.MAX_QUANTITY;
      if (invalidQuantity) {
        throw new BusinessException(
          this.i18n.translate('error.MAX_QUANTITY_CART_DETAIL', {
            args: { quantity: CART_CONST.MAX_QUANTITY },
          }),
          ResponseCodeEnum.BAD_REQUEST,
        );
      }

      await this.cartRepository.updateOne(
        { user: userId, 'cartDetails.product': productId },
        { $inc: { 'cartDetails.$.quantity': quantity } },
      );
    } else {
      await this.cartRepository.updateOne(
        { user: userId },
        {
          $push: {
            cartDetails: {
              product: new ObjectId(productId),
              quantity,
            },
          },
        },
      );
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async removeProduct(request: RemoveProductToCartRequestDto) {
    const { productId, quantity, userId } = request;

    const product = await this.productRepository.getDetail(productId);
    if (isEmpty(product)) {
      throw new BusinessException(
        this.i18n.translate('error.PRODUCT_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    if (product.status !== PRODUCT_STATUS_ENUM.ACTIVE) {
      throw new BusinessException(
        this.i18n.translate('error.PRODUCT_STATUS_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const cart = await this.cartRepository
      .findOne({ user: userId })
      .populate<{
        cartDetails: Array<{ product: Product; quantity: number }>;
      }>({
        path: 'cartDetails.product',
      })
      .lean();

    if (isEmpty(cart)) {
      throw new BusinessException(
        this.i18n.translate('error.CART_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const exitsProduct = cart.cartDetails.find((item) => {
      return item.product._id.toString() === productId.toString();
    });

    if (isEmpty(exitsProduct)) {
      throw new BusinessException(
        this.i18n.translate('error.PRODUCT_NOT_EXIST_CART'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const currentQuantity = exitsProduct.quantity;
    const newQuantity = currentQuantity - quantity;

    if (newQuantity < 0) {
      throw new BusinessException(
        this.i18n.translate('error.QUANTITY_INVALID'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    } else if (newQuantity === 0) {
      await this.cartRepository.updateOne(
        { user: userId },
        { $pull: { cartDetails: { product: productId } } },
      );
    } else {
      await this.cartRepository.updateOne(
        { user: userId, 'cartDetails.product': productId },
        { $set: { 'cartDetails.$.quantity': newQuantity } },
      );
    }

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async removeFastProduct(request: RemoveFastProductToCartRequestDto) {
    const { userId, productId } = request;

    const product = await this.productRepository.getDetail(productId);
    if (isEmpty(product)) {
      throw new BusinessException(
        this.i18n.translate('error.PRODUCT_NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const cart = await this.cartRepository
      .findOne({ user: userId })
      .populate<{
        cartDetails: Array<{ product: Product; quantity: number }>;
      }>({
        path: 'cartDetails.product',
      })
      .lean();

    const existProduct = cart.cartDetails.find((item) => {
      return item.product._id.toString() === productId.toString();
    });

    if (isEmpty(existProduct)) {
      throw new BusinessException(
        this.i18n.translate('error.PRODUCT_NOT_EXIST_CART'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    await this.cartRepository.updateOne(
      { user: userId },
      { $pull: { cartDetails: { product: productId } } },
    );

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async reset(user: User) {
    const { id } = user;
    await this.cartRepository.updateOne({ user: id }, { cartDetails: [] });

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }
}
