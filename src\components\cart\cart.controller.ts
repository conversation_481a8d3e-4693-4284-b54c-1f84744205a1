import {
  Get,
  Put,
  Body,
  Post,
  Delete,
  Request,
  Controller,
} from '@nestjs/common';
import { isEmpty } from 'lodash';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { CartService } from './cart.service';
import { BaseDto } from '@core/dto/base.request.dto';
import { AddProductToCartRequestDto } from './dto/request/add-product-to-cart.request.dto';
import { RemoveProductToCartRequestDto } from './dto/request/remove-product-to-cart.request.dto';
import { RemoveFastProductToCartRequestDto } from './dto/request/remove-fast-product-to-cart.request.dto';

@ApiBearerAuth()
@Controller('carts')
export class CartController {
  constructor(private readonly cartService: CartService) {}

  @Post('/init')
  @ApiOperation({
    tags: ['Cart'],
    summary: 'Khởi tạo giỏ hàng',
    description: 'Khởi tạo giỏ hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async init(@Request() request: BaseDto) {
    return await this.cartService.init(request.user);
  }

  @Get('/me')
  @ApiOperation({
    tags: ['Cart'],
    summary: 'Thông tin giỏ hàng của tôi',
    description: 'Thông tin giỏ hàng của tôi',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async me(@Request() request: BaseDto) {
    return await this.cartService.myCart(request.user);
  }

  @Put('/add-product')
  @ApiOperation({
    tags: ['Cart'],
    summary: 'Thêm sản phẩm vào giỏ hàng',
    description: 'Thêm sản phẩm vào giỏ hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async addProduct(@Body() payload: AddProductToCartRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.cartService.addProduct(request);
  }

  @Put('/remove-product')
  @ApiOperation({
    tags: ['Cart'],
    summary: 'Gỉảm số lượng sản phẩm từ giỏ hàng',
    description: 'Gỉảm số lượng sản phẩm từ giỏ hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async removeProduct(@Body() payload: RemoveProductToCartRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.cartService.removeProduct(request);
  }

  @Put('/remove-fast-product')
  @ApiOperation({
    tags: ['Cart'],
    summary: 'Xóa toàn bộ sản phẩm từ giỏ hàng',
    description: 'Xóa toàn bộ sản phẩm từ giỏ hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async removeFastProduct(@Body() payload: RemoveFastProductToCartRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.cartService.removeFastProduct(request);
  }

  @Delete('/reset')
  @ApiOperation({
    tags: ['Cart'],
    summary: 'Xoá toàn bộ sản phẩm trong giỏ hàng',
    description: 'Xoá toàn bộ sản phẩm trong giỏ hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async reset(@Request() request: BaseDto) {
    return await this.cartService.reset(request.user);
  }
}
