import { Transform } from 'class-transformer';
import { Is<PERSON><PERSON>, IsNotEmpty, IsNumber } from 'class-validator';

import { BaseDto } from '@core/dto/base.request.dto';
import { SUMMARY_TIME_TYPE } from '@components/dashboard/dashboard.constant';

export class GetSummaryOrderRequestDto extends BaseDto {
  @IsNumber()
  @IsNotEmpty()
  @Transform(({ value }) => Number(value))
  @IsEnum(SUMMARY_TIME_TYPE)
  type: SUMMARY_TIME_TYPE;
}
