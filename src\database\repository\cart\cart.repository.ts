import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

import { Cart } from '@database/schemas/cart.model';
import { CartRepositoryInterface } from './cart.repository.interface';
import { BaseAbstractRepository } from '@core/repository/base.abstract.repository';
export class CartRepository
  extends BaseAbstractRepository<Cart>
  implements CartRepositoryInterface
{
  constructor(
    @InjectModel('Cart')
    private readonly cartModel: Model<Cart>,
  ) {
    super(cartModel);
  }
}
