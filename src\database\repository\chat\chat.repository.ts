import { isEmpty } from 'lodash';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

import { getRegexByValue } from '@utils/common';
import { BOOLEAN_ENUM } from '@constant/app.enum';
import { PaginationQuery } from '@core/dto/pagination.query';
import { Chat as ChatModel } from '@database/schemas/chat.model';
import { ChatRepositoryInterface } from './chat.repository.interface';
import { LastChat as LastChatModel } from '@database/schemas/last-chat.model';
import { BaseAbstractRepository } from '@core/repository/base.abstract.repository';
import { ListChatRequestDto } from '@components/chat/dto/request/list-chat.request.dto';
import { CreateChatRequestDto } from '@components/chat/dto/request/create-chat.request.dto';

export class ChatRepository
  extends BaseAbstractRepository<ChatModel>
  implements ChatRepositoryInterface
{
  constructor(
    @InjectModel('Chat')
    private readonly chatModel: Model<ChatModel>,

    @InjectModel('LastChat')
    private readonly lastChatModel: Model<LastChatModel>,
  ) {
    super(chatModel);
  }

  createEntity(data: CreateChatRequestDto): ChatModel {
    const entity = new this.chatModel();

    entity.message = data.message;
    entity.receiver = data.receiver; // người nhận
    entity.sender = data.userId ?? data.sender; // người gửi
    entity.isUserMsg = data.receiver !== 'admin' ? 0 : 1;

    return entity;
  }

  async listForUser(
    request: ListChatRequestDto,
  ): Promise<{ data: ChatModel[]; total: number }> {
    const { userId, page, limit } = request;

    const take = limit;
    const skip = (page - 1) * limit;

    const filterObj = {
      $or: [
        {
          sender: userId,
          isUserMsg: BOOLEAN_ENUM.TRUE,
        },
        {
          receiver: userId,
          isUserMsg: BOOLEAN_ENUM.FALSE,
        },
      ],
    };

    const [messages, total] = await Promise.all([
      this.chatModel
        .find(filterObj)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(take)
        .exec(),
      this.chatModel.countDocuments(filterObj).exec(),
      ,
    ]);

    return { data: messages, total };
  }

  async listForAdmin(
    request: ListChatRequestDto,
    userId: string,
  ): Promise<{ data: ChatModel[]; total: number }> {
    const { page, limit } = request;

    const take = limit;
    const skip = (page - 1) * limit;

    const filterObj = {
      $or: [
        {
          sender: userId,
        },
        {
          receiver: userId,
        },
      ],
    };

    const [messages, total] = await Promise.all([
      this.chatModel
        .find(filterObj)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(take)
        .exec(),
      this.chatModel.countDocuments(filterObj).exec(),
      ,
    ]);

    return { data: messages, total };
  }

  async listLastChatForAdmin(
    request: PaginationQuery,
  ): Promise<{ data: any[]; total: number }> {
    const { keyword, take, skip } = request;

    let filterObj = {};

    if (!isEmpty(keyword)) {
      const filterByKeyword = getRegexByValue(keyword);
      filterObj = {
        $or: [
          { 'user.email': filterByKeyword },
          { 'user.fullname': filterByKeyword },
        ],
      };
    }

    const [messages, total] = await Promise.all([
      this.lastChatModel
        .find(filterObj)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(take)
        .populate([
          {
            path: 'chat',
            select: 'message',
          },
          {
            path: 'user',
            select: 'email fullname avatar',
          },
        ])
        .exec(),
      this.lastChatModel.countDocuments(filterObj).exec(),
    ]);

    return { data: messages, total };
  }
}
