import * as mongoose from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseModel } from '@core/schema/base.model';

@Schema({
  timestamps: true,
  collection: 'order-summaries',
  collation: { locale: 'vi' },
})
export class OrderSummary extends BaseModel {
  @Prop({
    type: Date,
    required: true,
  })
  date: Date;

  @Prop({
    type: Number,
    default: 0,
  })
  quantity: number;

  @Prop({
    type: Number,
    default: 0,
  })
  totalPrice: number;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true,
    index: true,
  })
  product: string;
}

export const OrderSummarySchema = SchemaFactory.createForClass(OrderSummary);

OrderSummarySchema.index({ date: 1 });
OrderSummarySchema.index({ product: 1 });
OrderSummarySchema.index({ createdAt: -1 });
OrderSummarySchema.index({ date: 1, product: 1 });

export type OrderSummaryDocument = OrderSummary & mongoose.Document;
