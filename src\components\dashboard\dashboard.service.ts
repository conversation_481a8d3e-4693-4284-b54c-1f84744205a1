import { keyBy } from 'lodash';
import * as moment from 'moment';
import { Types } from 'mongoose';
import { I18nService } from 'nestjs-i18n';
import { OnEvent } from '@nestjs/event-emitter';
import { Inject, Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';

import {
  SUMMARY_TIME_TYPE,
  SummaryRevenueItem,
} from '@components/dashboard/dashboard.constant';
import { EVENT_ENUM } from '@constant/event.enum';
import { Order } from '@database/schemas/order.model';
import { ResponseBuilder } from '@utils/response-builder';
import { getStartAndEndTime } from '@helpers/date.helper';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { UserRepositoryInterface } from '@database/repository/user/user.repository.interface';
import { OrderRepositoryInterface } from '@database/repository/order/order.repository.interface';
import { ProductRepositoryInterface } from '@database/repository/product/product.repository.interface';
import { GetSummaryOrderRequestDto } from '@components/dashboard/dto/request/get-summary-order.request.dto';
import { GetSummaryRevenueRequestDto } from '@components/dashboard/dto/request/get-summary-revenue.request.dto';
import { GetTopSellingProductRequestDto } from '@components/dashboard/dto/request/get-top-selling-product.request.dto';
import { OrderSummaryRepositoryInterface } from '@database/repository/order-summary/order-summary.repository.interface';
import { GetTopSellingProductResponseDto } from '@components/dashboard/dto/response/get-top-selling-product.response.dto';

@Injectable()
export class DashboardService {
  constructor(
    private readonly i18n: I18nService,

    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,

    @Inject('OrderRepositoryInterface')
    private readonly orderRepository: OrderRepositoryInterface,

    @Inject('ProductRepositoryInterface')
    private readonly productRepository: ProductRepositoryInterface,

    @Inject('OrderSummaryRepositoryInterface')
    private readonly orderSummaryRepository: OrderSummaryRepositoryInterface,
  ) {}

  async getSummaryUsers() {
    const summary = await this.userRepository.getSummaryUsers();

    return new ResponseBuilder({
      items: summary,
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async getSummaryOrders(request: GetSummaryOrderRequestDto) {
    const { summary, start, end } =
      await this.orderRepository.getSummaryOrders(request);

    return new ResponseBuilder({
      items: summary,
      start,
      end,
      type: request?.type,
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async getTopSellingProducts(request: GetTopSellingProductRequestDto) {
    const { summary, start, end } =
      await this.orderSummaryRepository.getTopSellingProducts(request);

    const products = await this.productRepository.find({
      _id: {
        $in: summary?.map((item) => new Types.ObjectId(item?._id)),
      },
    });

    const productsMap = keyBy(products, '_id');

    for (const item of summary) {
      item['product'] = productsMap[item?._id];
    }

    const response = plainToInstance(GetTopSellingProductResponseDto, summary, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      start,
      end,
      type: request?.type,
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  private getRandomPrice = (
    min: number = 100000,
    max: number = 5000000,
  ): number => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  };

  private getRandomQuantity = (min: number = 5, max: number = 100): number => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  };

  async getSummaryRevenue(request: GetSummaryRevenueRequestDto) {
    const { type } = request;
    const { start, end } = getStartAndEndTime(type);

    const summary: SummaryRevenueItem[] = [];

    switch (type) {
      case SUMMARY_TIME_TYPE.TODAY:
        // Trả về 1 item cho ngày hôm nay
        summary.push({
          start: start,
          end: end,
          totalPrice: this.getRandomPrice(500000, 2000000),
          totalQuantity: this.getRandomQuantity(10, 50),
        });
        break;

      case SUMMARY_TIME_TYPE.WEEK:
        // Trả về 7 item cho 7 ngày trong tuần
        for (let i = 0; i < 7; i++) {
          const dayStart = moment(start).add(i, 'days').startOf('day');
          const dayEnd = moment(start).add(i, 'days').endOf('day');

          summary.push({
            start: dayStart.toDate(),
            end: dayEnd.toDate(),
            totalQuantity: this.getRandomQuantity(5, 40),
            totalPrice: this.getRandomPrice(200000, 1500000),
          });
        }
        break;

      case SUMMARY_TIME_TYPE.MONTH:
        // Trả về 4 item cho 4 tuần trong tháng
        const monthStart = moment(start);
        for (let i = 0; i < 4; i++) {
          const weekStart = monthStart.clone().add(i, 'weeks').startOf('week');
          const weekEnd = monthStart.clone().add(i, 'weeks').endOf('week');

          // Đảm bảo không vượt quá tháng hiện tại
          if (weekStart.month() !== monthStart.month()) {
            weekStart.startOf('month');
          }
          if (weekEnd.month() !== monthStart.month()) {
            weekEnd.endOf('month');
          }

          summary.push({
            start: weekStart.toDate(),
            end: weekEnd.toDate(),
            totalPrice: this.getRandomPrice(1000000, 8000000),
            totalQuantity: this.getRandomQuantity(50, 300),
          });
        }
        break;

      case SUMMARY_TIME_TYPE.YEAR:
        // Trả về 12 item cho 12 tháng trong năm
        const yearStart = moment(start);
        for (let i = 0; i < 12; i++) {
          const monthStart = yearStart
            .clone()
            .add(i, 'months')
            .startOf('month');
          const monthEnd = yearStart.clone().add(i, 'months').endOf('month');

          summary.push({
            start: monthStart.toDate(),
            end: monthEnd.toDate(),
            totalPrice: this.getRandomPrice(5000000, 50000000),
            totalQuantity: this.getRandomQuantity(200, 1500),
          });
        }
        break;

      default:
        throw new Error('Invalid summary order type');
    }

    return new ResponseBuilder({
      items: summary,
      start,
      end,
      type: request?.type,
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  @OnEvent(EVENT_ENUM.SYNC_DATA_ORDER_SUMMARY)
  async syncDataTopSellingProducts(order: Order): Promise<void> {
    const { completedAt, orderDetails } = order;
    const startOfDay = moment(completedAt).startOf('day').toDate();

    const condition = {
      date: startOfDay,
      product: {
        $in: orderDetails?.map(
          (orderDetail) => new Types.ObjectId(orderDetail?.product),
        ),
      },
    };

    const summaryExists = await this.orderSummaryRepository.find(condition);
    const summaryExistsMap = keyBy(summaryExists, 'product');

    const bulkWrites = [];

    for (const orderDetail of orderDetails) {
      const summaryExists = summaryExistsMap[orderDetail?.product];
      const itemTotalPrice = orderDetail?.price * orderDetail?.quantity;

      if (summaryExists) {
        bulkWrites.push({
          updateOne: {
            filter: {
              _id: summaryExists?._id,
            },
            update: {
              $inc: {
                totalPrice: itemTotalPrice,
                quantity: orderDetail?.quantity,
              },
            },
          },
        });
      } else {
        bulkWrites.push({
          insertOne: {
            document: {
              date: startOfDay,
              totalPrice: itemTotalPrice,
              quantity: orderDetail?.quantity,
              product: new Types.ObjectId(orderDetail?.product),
            },
          },
        });
      }
    }

    if (!bulkWrites || bulkWrites.length === 0) return;

    await this.orderSummaryRepository.bulkWrite(bulkWrites);
  }
}
