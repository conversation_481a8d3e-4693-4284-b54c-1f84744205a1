import {
  Put,
  Get,
  Body,
  Post,
  Param,
  Query,
  Delete,
  Request,
  Controller,
} from '@nestjs/common';
import { isEmpty } from 'lodash';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { mergePayload } from '@utils/common';
import { AddressService } from './address.service';
import { BaseDto } from '@core/dto/base.request.dto';
import { IdParamDto } from '@core/dto/param-id.request.dto';
import { GetMyAddressRequestDto } from './dto/request/get-my-address.request.dto';
import { UpdateAddressRequestDto } from './dto/request/update-address.request.dto';
import { CreateAddressRequestDto } from './dto/request/create-address.request.dto';

@ApiBearerAuth()
@Controller('addresses')
export class AddressController {
  constructor(private readonly addressService: AddressService) {}

  @Get('/')
  @ApiOperation({
    tags: ['Address'],
    summary: '<PERSON><PERSON> sách điểm giao hàng của bạn',
    description: '<PERSON>h sách điểm giao hàng của bạn',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async list(@Query() query: GetMyAddressRequestDto, @Request() req: BaseDto) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.addressService.list({
      ...request,
      user: req?.user,
      userId: req?.user?.id,
    });
  }

  @Post('/')
  @ApiOperation({
    tags: ['Address'],
    summary: 'Tạo mới điểm giao hàng',
    description: 'Tạo mới điểm giao hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async create(@Body() payload: CreateAddressRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.addressService.create(request);
  }

  @Put('/:id')
  @ApiOperation({
    tags: ['Address'],
    summary: 'Cập nhật thông tin điểm giao hàng',
    description: 'Cập nhật thông tin điểm giao hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async update(
    @Param() param: IdParamDto,
    @Body() payload: UpdateAddressRequestDto,
  ) {
    const { request, responseError } = mergePayload(payload, param);

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.addressService.update(request);
  }

  @Delete('/:id')
  @ApiOperation({
    tags: ['Address'],
    summary: 'Xóa điểm giao hàng',
    description: 'Xóa điểm giao hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async delete(@Param() param: IdParamDto) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.addressService.delete(request);
  }

  @Put('/:id/set-default')
  @ApiOperation({
    tags: ['Address'],
    summary: 'Cài điểm giao hàng mặc định',
    description: 'Cài điểm giao hàng mặc định',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async setDefaultAddress(@Param() param: IdParamDto) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.addressService.setDefaultAddress(request);
  }
}
