import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>,
  IsMongoId,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eng<PERSON>,
  IsNotEmpty,
  IsOptional,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { BaseDto } from '@core/dto/base.request.dto';
import {
  PRODUCT_CONST,
  PRODUCT_STATUS_ENUM,
} from '@components/product/product.constant';

export class CreateProductRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MinLength(PRODUCT_CONST.NAME.MIN_LENGTH)
  @MaxLength(PRODUCT_CONST.NAME.MAX_LENGTH)
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MinLength(PRODUCT_CONST.CODE.MIN_LENGTH)
  @MaxLength(PRODUCT_CONST.CODE.MAX_LENGTH)
  code: string;

  @ApiProperty()
  @IsString()
  @IsMongoId()
  @IsNotEmpty()
  category: string;

  @ApiProperty()
  @IsString()
  @MinLength(PRODUCT_CONST.DESCRIPTION.MIN_LENGTH)
  @MaxLength(PRODUCT_CONST.DESCRIPTION.MAX_LENGTH)
  @IsOptional()
  description: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  slug?: string;

  @ApiProperty()
  @IsString()
  @IsUrl()
  @IsOptional()
  image?: string;

  @ApiProperty()
  @Min(PRODUCT_CONST.PRICE.MIN_VALUE)
  @Max(PRODUCT_CONST.PRICE.MAX_VALUE)
  @IsNumber()
  @IsNotEmpty()
  price: number;

  @ApiProperty()
  @IsEnum(PRODUCT_STATUS_ENUM)
  @IsOptional()
  status: PRODUCT_STATUS_ENUM;
}
