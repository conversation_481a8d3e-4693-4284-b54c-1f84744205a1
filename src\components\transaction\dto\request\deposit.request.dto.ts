import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber } from 'class-validator';

import { BaseDto } from '@core/dto/base.request.dto';
import { PAYMENT_TYPE_ENUM } from '@components/transaction/transaction.constant';

export class DepositRequestDto extends BaseDto {
  @ApiProperty({
    description: 'Số tiền nạp',
    example: 100000,
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'Loại thanh toán',
    example: PAYMENT_TYPE_ENUM.VNPAY,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(PAYMENT_TYPE_ENUM)
  paymentType: PAYMENT_TYPE_ENUM;
}
