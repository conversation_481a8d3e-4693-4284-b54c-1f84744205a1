import { Category } from '@database/schemas/category.model';
import { BaseInterfaceRepository } from '@core/repository/base.interface.repository';
import { UpdateCategoryRequestDto } from '@components/category/dto/request/update-category.request.dto';
import { CreateCategoryRequestDto } from '@components/category/dto/request/create-category.request.dto';
import { GetListCategoryRequestDto } from '@components/category/dto/request/get-list-category.request.dto';

export interface CategoryRepositoryInterface
  extends BaseInterfaceRepository<Category> {
  createEntity(data: CreateCategoryRequestDto): Category;

  getDetail(id: string): Promise<Category | null>;

  updateEntity(entity: Category, data: UpdateCategoryRequestDto): Category;

  list(
    request: GetListCategoryRequestDto,
    isExport?: boolean,
  ): Promise<{ data: Category[]; total: number }>;

  findPopular(): Promise<Category[]>;

  findByKeywords(keywords: string[]): Promise<Category[]>;
}
