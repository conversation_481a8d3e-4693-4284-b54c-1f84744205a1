import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { Transaction } from '@database/schemas/transaction.model';
import { BaseInterfaceRepository } from '@core/repository/base.interface.repository';
import { GetListTransactionRequestDto } from '@components/transaction/dto/request/get-list-transaction.request.dto';

export interface TransactionRepositoryInterface
  extends BaseInterfaceRepository<Transaction> {
  createEntity(request: any): Transaction;

  list(
    request: GetListTransactionRequestDto,
    role?: USER_ROLE_ENUM,
  ): Promise<{ data: Transaction[]; total: number }>;
}
