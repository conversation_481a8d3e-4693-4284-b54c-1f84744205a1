import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

import { BaseResponseDto } from '@core/dto/base.response.dto';

export class ProductInCartResponseDto extends BaseResponseDto {
  @ApiProperty()
  @Expose()
  code: string;

  @ApiProperty()
  @Expose()
  price: number;

  @ApiProperty()
  @Expose()
  description: number;

  @ApiProperty()
  @Expose()
  image: number;
}

export class CartDetailResponseDto extends BaseResponseDto {
  @ApiProperty({ type: ProductInCartResponseDto })
  @Expose()
  @Type(() => ProductInCartResponseDto)
  product: ProductInCartResponseDto;

  @ApiProperty()
  @Expose()
  quantity: number;

  @ApiProperty()
  @Expose()
  subtotal: number;
}

export class MyCartResponseDto extends BaseResponseDto {
  @ApiProperty()
  @Expose()
  totalQuantity: number;

  @ApiProperty()
  @Expose()
  totalPrice: number;

  @ApiProperty({ type: [CartDetailResponseDto] })
  @Expose()
  @Type(() => CartDetailResponseDto)
  cartDetails: CartDetailResponseDto[];
}
