import {
  IsUrl,
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>otEmpty,
  IsOptional,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { BaseDto } from '@core/dto/base.request.dto';
import { CATEGORY_CONST } from '@components/category/category.constant';

export class CreateCategoryRequestDto extends BaseDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MinLength(CATEGORY_CONST.NAME.MIN_LENGTH)
  @MaxLength(CATEGORY_CONST.NAME.MAX_LENGTH)
  name: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MinLength(CATEGORY_CONST.CODE.MIN_LENGTH)
  @MaxLength(CATEGORY_CONST.CODE.MAX_LENGTH)
  code: string;

  @ApiProperty()
  @IsString()
  @MinLength(CATEGORY_CONST.DESCRIPTION.MIN_LENGTH)
  @MaxLength(CATEGORY_CONST.DESCRIPTION.MAX_LENGTH)
  @IsOptional()
  description: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  slug?: string;

  @ApiProperty()
  @IsString()
  @IsUrl()
  @IsOptional()
  image?: string;
}
