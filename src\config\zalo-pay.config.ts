import { registerAs } from '@nestjs/config';
import { IsString, IsOptional } from 'class-validator';

import { ZaloPayConfig } from './config.type';
import validateConfig from '@utils/validate-config';

class EnvironmentVariablesValidator {
  @IsString()
  @IsOptional()
  ZALO_API_URL: string;

  @IsString()
  @IsOptional()
  ZALO_APP_ID: string;

  @IsString()
  @IsOptional()
  ZALO_APP_USER: string;

  @IsString()
  @IsOptional()
  ZALO_CALLBACK_URL: string;

  @IsString()
  @IsOptional()
  ZALO_KEY1: string;

  @IsString()
  @IsOptional()
  ZALO_KEY2: string;

  @IsString()
  @IsOptional()
  ZALO_REDIRECT_URL: string;
}

export default registerAs<ZaloPayConfig>('zaloPay', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    apiUrl: process.env.ZALO_API_URL || '',
    appId: process.env.ZALO_APP_ID || '',
    appUser: process.env.ZALO_APP_USER || '',
    callbackUrl: process.env.ZALO_CALLBACK_URL || '',
    key1: process.env.ZALO_KEY1 || '',
    key2: process.env.ZALO_KEY2 || '',
    redirectUrl: process.env.ZALO_REDIRECT_URL || '',
  };
});
