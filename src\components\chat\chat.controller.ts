import {
  Get,
  <PERSON>s,
  Post,
  Query,
  Param,
  Request,
  UseG<PERSON><PERSON>,
  Controller,
} from '@nestjs/common';
import { isEmpty } from 'lodash';
import { Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { ChatService } from './chat.service';
import { RoleGuard } from '@core/guards/role.guard';
import { AllConfigType } from '@config/config.type';
import { BaseDto } from '@core/dto/base.request.dto';
import { Roles } from '@core/decorators/roles.decorator';
import { Public } from '@core/decorators/public.decorator';
import { IdParamDto } from '@core/dto/param-id.request.dto';
import { PaginationQuery } from '@core/dto/pagination.query';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { ListChatRequestDto } from './dto/request/list-chat.request.dto';

@ApiTags('Chat')
@Controller('chat')
export class ChatController {
  constructor(
    private readonly chatService: ChatService,

    private readonly configService: ConfigService<AllConfigType>,
  ) {}

  @Public()
  @Get('test')
  async getChatTestPage(@Res() res: Response) {
    const socketUrl = this.configService.get('app.socketUrl', {
      infer: true,
    });
    return res.render('chat.ejs', {
      title: 'Chat Test - HAUI Food',
      socketUrl,
    });
  }

  @Post('/reset')
  @ApiOperation({
    tags: ['Chat'],
    summary: 'Reset với quản trị viên',
    description: 'Reset với quản trị viên',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async reset(@Request() request: BaseDto) {
    return await this.chatService.reset(request);
  }

  @Get('/user/history')
  @ApiOperation({
    tags: ['Chat'],
    summary: 'Danh sách tin nhắn với người hỗ trợ (Admin)',
    description: 'Danh sách tin nhắn với người hỗ trợ (Admin)',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async listMessageSupport(
    @Request() req: BaseDto,
    @Query() query: ListChatRequestDto,
  ) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.chatService.listForUser({
      ...request,
      user: req?.user,
      userId: req?.userId,
    });
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN, USER_ROLE_ENUM.VIEWER)
  @Get('/user/:id/history')
  @ApiOperation({
    tags: ['Chat'],
    summary: 'Danh sách tin nhắn giữa admin và một người dùng cụ thể',
    description: 'Danh sách tin nhắn giữa admin và một người dùng cụ thể',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async listMessageBetweenAdminAndUser(
    @Param() param: IdParamDto,
    @Query() query: ListChatRequestDto,
  ) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.chatService.listForAdmin(request, param.request.id);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN, USER_ROLE_ENUM.VIEWER)
  @Get('/users')
  @ApiOperation({
    tags: ['Chat'],
    summary: 'Danh sách nhưng người liên hệ gần nhất dành cho Admin',
    description: 'Danh sách nhưng người liên hệ gần nhất cho Admin',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async listLastChatForAdmin(@Query() query: PaginationQuery) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.chatService.listLastChatForAdmin(request);
  }
}
