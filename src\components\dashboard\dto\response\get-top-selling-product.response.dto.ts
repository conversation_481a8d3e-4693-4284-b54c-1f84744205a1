import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

import { BaseResponseDto } from '@core/dto/base.response.dto';
import { ProductInCartResponseDto } from '@components/cart/dto/response/my-cart.response.dto';

export class GetTopSellingProductResponseDto extends BaseResponseDto {
  @ApiProperty()
  @Expose()
  totalQuantity: number;

  @ApiProperty()
  @Expose()
  totalPrice: number;

  @ApiProperty()
  @Expose()
  @Type(() => ProductInCartResponseDto)
  product: ProductInCartResponseDto;
}
