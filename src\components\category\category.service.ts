import slugify from 'slugify';
import { isEmpty, keyBy } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { plainToInstance } from 'class-transformer';
import { Inject, Injectable } from '@nestjs/common';

import {
  formatDateVN,
  exportOneSheetUtil,
  formatStringExport,
  createExportHeaders,
} from '@helpers/export.helper';
import { ResponseBuilder } from '@utils/response-builder';
import { IdParamDto } from '@core/dto/param-id.request.dto';
import { BaseResponseDto } from '@core/dto/base.response.dto';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { CreateCategoryRequestDto } from './dto/request/create-category.request.dto';
import { UpdateCategoryRequestDto } from './dto/request/update-category.request.dto';
import { BusinessException } from '@core/exception-filter/business-exception.filter';
import { GetListCategoryRequestDto } from './dto/request/get-list-category.request.dto';
import { GetDetailCategoryRequestDto } from './dto/request/get-detail-category.request.dto';
import { UserRepositoryInterface } from '@database/repository/user/user.repository.interface';
import { GetDetailCategoryResponseDto } from './dto/response/get-category-detail.response.dto';
import { ProductRepositoryInterface } from '@database/repository/product/product.repository.interface';
import { CategoryRepositoryInterface } from '@database/repository/category/category.repository.interface';

@Injectable()
export class CategoryService {
  constructor(
    private readonly i18n: I18nService,

    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,

    @Inject('ProductRepositoryInterface')
    private readonly productRepository: ProductRepositoryInterface,

    @Inject('CategoryRepositoryInterface')
    private readonly categoryRepository: CategoryRepositoryInterface,
  ) {}
  async create(request: CreateCategoryRequestDto) {
    const { code, name } = request;

    const codeExist = await this.categoryRepository.findOne({ code });
    if (!isEmpty(codeExist)) {
      throw new BusinessException(
        this.i18n.translate('error.CATEGORY_EXIST', {
          args: { code },
        }),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const categoryEntity = this.categoryRepository.createEntity({
      ...request,
      slug: `${code}-${slugify(name, { lower: true })}`,
    });

    await categoryEntity.save();

    const response = plainToInstance(BaseResponseDto, categoryEntity, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.CREATED)
      .withMessage(this.i18n.translate('message.CREATE_SUCCESS'))
      .build();
  }

  async list(request: GetListCategoryRequestDto) {
    const { data, total } = await this.categoryRepository.list(request);

    const response = plainToInstance(GetDetailCategoryResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      meta: { total, page: request.page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async getDetail(request: GetDetailCategoryRequestDto) {
    const { id } = request;
    const category = await this.categoryRepository.getDetail(id);

    if (isEmpty(category)) {
      throw new BusinessException(
        this.i18n.translate('error.NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const response = plainToInstance(GetDetailCategoryResponseDto, category, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async update(request: UpdateCategoryRequestDto) {
    const { id, name, code } = request;

    const category = await this.categoryRepository.getDetail(id);
    if (isEmpty(category)) {
      throw new BusinessException(
        this.i18n.translate('error.NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const categoryEntity = this.categoryRepository.updateEntity(category, {
      ...request,
      slug: `${code}-${slugify(name, { lower: true })}`,
    });

    await categoryEntity.save();

    const response = plainToInstance(
      GetDetailCategoryResponseDto,
      categoryEntity,
      {
        excludeExtraneousValues: true,
      },
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.UPDATE_SUCCESS'))
      .build();
  }

  async delete(request: IdParamDto) {
    const { id, userId } = request;

    const category = await this.categoryRepository.getDetail(id);
    if (isEmpty(category)) {
      throw new BusinessException(
        this.i18n.translate('error.NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    const product = await this.productRepository.findOne({
      category: id,
    });
    if (!isEmpty(product)) {
      throw new BusinessException(
        this.i18n.translate('error.CATEGORY_HAVE_PRODUCT'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    category.deletedBy = userId;
    category.deletedAt = new Date();
    await category.save();

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.DELETE_SUCCESS'))
      .build();
  }

  async export(request: GetListCategoryRequestDto) {
    const { data: categories } = await this.categoryRepository.list(
      request,
      true,
    );

    if (isEmpty(categories)) {
      throw new BusinessException(
        this.i18n.translate('error.NO_DATA_RECORDS'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const userCreatedByIdsSet = new Set<string>();
    categories?.forEach((category) => {
      if (category.createdBy) {
        userCreatedByIdsSet.add(category.createdBy.toString());
      }
    });

    const userCreates = await this.userRepository.find({
      _id: {
        $in: [...userCreatedByIdsSet],
      },
    });

    const userMap = keyBy(userCreates, '_id');
    const categoryHeaders = createExportHeaders(
      this.i18n.translate('export.category.headers'),
    );
    const categorySheetName = this.i18n.translate('export.category.title');

    categories?.forEach((category) => {
      const createdBy = userMap[category.createdBy?.toString()];
      category['createdBy'] = formatStringExport(
        createdBy?.fullname,
        createdBy?.email,
      );
      category['createdAt'] = formatDateVN(category['createdAt']) as any;
    });

    const workbook = exportOneSheetUtil(
      categories,
      categorySheetName,
      categoryHeaders,
      categorySheetName,
    );

    const file = await workbook.xlsx.writeBuffer();
    // await workbook.xlsx.writeFile(`./category.xlsx`);

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('error.SUCCESS'))
      .withData(file)
      .build();
  }
}
