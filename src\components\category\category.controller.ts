import {
  Put,
  Get,
  Body,
  Post,
  Param,
  Query,
  Delete,
  UseGuards,
  Controller,
  UseInterceptors,
} from '@nestjs/common';
import { isEmpty } from 'lodash';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { mergePayload } from '@utils/common';
import { RoleGuard } from '@core/guards/role.guard';
import { CategoryService } from './category.service';
import { Roles } from '@core/decorators/roles.decorator';
import { Public } from '@core/decorators/public.decorator';
import { IdParamDto } from '@core/dto/param-id.request.dto';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { UpdateCategoryRequestDto } from './dto/request/update-category.request.dto';
import { CreateCategoryRequestDto } from './dto/request/create-category.request.dto';
import { GetListCategoryRequestDto } from './dto/request/get-list-category.request.dto';
import { GetDetailCategoryRequestDto } from './dto/request/get-detail-category.request.dto';

@ApiBearerAuth()
@Controller('categories')
export class CategoryController {
  constructor(private readonly categoryService: CategoryService) {}

  @Public()
  @Get('/')
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({
    tags: ['Category'],
    summary: 'Danh sách loại sản phẩm',
    description: 'Danh sách loại sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async list(@Query() query: GetListCategoryRequestDto) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.categoryService.list(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Post('/')
  @ApiOperation({
    tags: ['Category'],
    summary: 'Tạo mới loại sản phẩm',
    description: 'Tạo mới loại sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async create(@Body() payload: CreateCategoryRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.categoryService.create(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Put('/:id')
  @ApiOperation({
    tags: ['Category'],
    summary: 'Cập nhật thông tin loại sản phẩm',
    description: 'Cập nhật thông tin loại sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async update(
    @Param() param: IdParamDto,
    @Body() payload: UpdateCategoryRequestDto,
  ) {
    const { request, responseError } = mergePayload(payload, param);

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.categoryService.update(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Delete('/:id')
  @ApiOperation({
    tags: ['Category'],
    summary: 'Xóa thông tin loại sản phẩm',
    description: 'Xóa thông tin loại sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async delete(@Param() param: IdParamDto) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.categoryService.delete(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Get('/export')
  @ApiOperation({
    tags: ['Users'],
    summary: 'Xuất danh sách thể loại',
    description: 'Xuất danh sách thể loại',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async export(@Query() query: GetListCategoryRequestDto) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.categoryService.export(request);
  }

  @Public()
  @Get('/:id')
  @ApiOperation({
    tags: ['Category'],
    summary: 'Chi tiết loại sản phẩm',
    description: 'Chi tiết loại sản phẩm',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async detail(@Param() param: GetDetailCategoryRequestDto) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.categoryService.getDetail(request);
  }
}
