import {
  Get,
  Res,
  Body,
  Post,
  Param,
  Query,
  Request,
  UseGuards,
  Controller,
} from '@nestjs/common';
import { isEmpty } from 'lodash';
import { Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { RoleGuard } from '@core/guards/role.guard';
import { AllConfigType } from '@config/config.type';
import { BaseDto } from '@core/dto/base.request.dto';
import { Roles } from '@core/decorators/roles.decorator';
import { TransactionService } from './transaction.service';
import { Public } from '@core/decorators/public.decorator';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { DepositRequestDto } from './dto/request/deposit.request.dto';
import { VnPayReturnRequestDto } from '@components/order/dto/request/vnpay-return.request.dto';
import { MomoReturnRequestDto } from '@components/transaction/dto/request/momo-return.request.dto';
import { ZaloPayReturnRequestDto } from '@components/transaction/dto/request/zalopay-return.request.dto';
import { HandleCallbackPayment } from '@components/transaction/dto/request/handle-callback-payment.request.dto';
import { VerifyTransactionRequestDto } from '@components/transaction/dto/request/verify-transaction.request.dto';
import { GetListTransactionRequestDto } from '@components/transaction/dto/request/get-list-transaction.request.dto';

@ApiBearerAuth()
@Controller('transactions')
export class TransactionController {
  constructor(
    private readonly transactionService: TransactionService,

    private readonly configService: ConfigService<AllConfigType>,
  ) {}

  @Public()
  @Get('/callback/:bank')
  @ApiOperation({
    tags: ['Transaction'],
    summary: 'Callback nạp tiền',
    description: 'Callback nạp tiền',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async handleCallbackPayment(
    @Res() res: Response,
    @Query()
    query:
      | MomoReturnRequestDto
      | VnPayReturnRequestDto
      | ZaloPayReturnRequestDto,
    @Param() param: HandleCallbackPayment,
  ) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    const { result, token } =
      await this.transactionService.handleCallbackPayment(
        Object.assign({}, query),
        request?.bank,
      );

    const appConfig = this.configService.getOrThrow('app', { infer: true });
    const frontendUrl = appConfig.frontendDomain || 'http://localhost:3000';

    if (!result) {
      return res.redirect(frontendUrl);
    }

    res.redirect(`${frontendUrl}/payment/verify?token=${token}`);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN)
  @Get('/')
  @ApiOperation({
    tags: ['Transaction'],
    summary: 'Danh sách giao dịch',
    description: 'Danh sách giao dịch',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async getListTransactions(
    @Request() req: BaseDto,
    @Query() query: GetListTransactionRequestDto,
  ) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.transactionService.listForAdmin({
      ...request,
      user: req?.user,
      userId: req?.userId,
    });
  }

  @Get('/me')
  @ApiOperation({
    tags: ['Transaction'],
    summary: 'Danh sách giao dịch dành cho người dùng',
    description: 'Danh sách giao dịch dành cho người dùng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async getMyTransactions(
    @Request() req: BaseDto,
    @Query() query: GetListTransactionRequestDto,
  ) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.transactionService.listForUser({
      ...request,
      user: req?.user,
      userId: req?.userId,
    });
  }

  @Post('/deposit')
  @ApiOperation({
    tags: ['Transaction'],
    summary: 'Nạp tiền',
    description: 'Nạp tiền',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async deposit(@Body() body: DepositRequestDto) {
    const { request, responseError } = body;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.transactionService.deposit(request);
  }

  @Post('/verify')
  @ApiOperation({
    tags: ['Transaction'],
    summary: 'Xác thực giao dịch thành công',
    description: 'Xác thực giao dịch thành công',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async verify(@Body() body: VerifyTransactionRequestDto) {
    const { request, responseError } = body;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.transactionService.verify(request);
  }
}
