import axios from 'axios';
import * as crypto from 'crypto';
import * as moment from 'moment';
import * as queryString from 'qs';
import { Connection } from 'mongoose';
import { I18nService } from 'nestjs-i18n';
import { ConfigService } from '@nestjs/config';
import { plainToInstance } from 'class-transformer';
import { InjectConnection } from '@nestjs/mongoose';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Cache, CACHE_MANAGER } from '@nestjs/cache-manager';

import {
  TRANSACTION_CONST,
  PAYMENT_TYPE_ENUM,
  TRANSACTION_ACTION_ENUM,
  PAYMENT_ENDPOINT_ENUM,
} from './transaction.constant';
import { AllConfigType } from '@config/config.type';
import { ResponseBuilder } from '@utils/response-builder';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { DepositRequestDto } from './dto/request/deposit.request.dto';
import { formatCurrency, generateRandomString } from '@helpers/string.helper';
import { BusinessException } from '@core/exception-filter/business-exception.filter';
import { UserRepositoryInterface } from '@database/repository/user/user.repository.interface';
import { VnPayReturnRequestDto } from '@components/order/dto/request/vnpay-return.request.dto';
import { MomoReturnRequestDto } from '@components/transaction/dto/request/momo-return.request.dto';
import { PaymentMomoResponseDto } from '@components/transaction/dto/response/payment-momo.response.dto';
import { ZaloPayReturnRequestDto } from '@components/transaction/dto/request/zalopay-return.request.dto';
import { PaymentZaloPayResponseDto } from '@components/transaction/dto/response/payment-zalopay.response.dto';
import { VerifyTransactionRequestDto } from '@components/transaction/dto/request/verify-transaction.request.dto';
import { TransactionRepositoryInterface } from '@database/repository/transaction/transaction.repository.interface';
import { GetListTransactionRequestDto } from '@components/transaction/dto/request/get-list-transaction.request.dto';
import { GetDetailTransactionResponseDto } from '@components/transaction/dto/response/get-detail-transaction.response.dto';

interface DepositData {
  userId: string;
  amount: number;
}

@Injectable()
export class TransactionService {
  private readonly logger = new Logger(TransactionService.name);

  private momoConfig = this.configService.get('momo', {
    infer: true,
  });

  private vnPayConfig = this.configService.get('vnPay', {
    infer: true,
  });

  private zaloPayConfig = this.configService.get('zaloPay', {
    infer: true,
  });

  constructor(
    @Inject(CACHE_MANAGER)
    private cacheManager: Cache,

    private readonly i18n: I18nService,

    @InjectConnection()
    private readonly connection: Connection,

    @Inject('UserRepositoryInterface')
    private readonly userRepository: UserRepositoryInterface,

    private readonly configService: ConfigService<AllConfigType>,

    @Inject('TransactionRepositoryInterface')
    private readonly transactionRepository: TransactionRepositoryInterface,
  ) {}

  async listForAdmin(request: GetListTransactionRequestDto) {
    const { data, total } = await this.transactionRepository.list(request);

    const response = plainToInstance(GetDetailTransactionResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      meta: { total, page: request.page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async listForUser(request: GetListTransactionRequestDto) {
    const { data, total } = await this.transactionRepository.list(
      request,
      USER_ROLE_ENUM.USER,
    );

    const response = plainToInstance(GetDetailTransactionResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      meta: { total, page: request.page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async deposit(request: DepositRequestDto) {
    const { user, userId, amount, paymentType } = request;

    if (amount < TRANSACTION_CONST.MIN_DEPOSIT_AMOUNT) {
      throw new BusinessException(
        this.i18n.translate('error.MIN_DEPOSIT_AMOUNT', {
          args: { min: TRANSACTION_CONST.MIN_DEPOSIT_AMOUNT },
        }),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    if (amount > TRANSACTION_CONST.MAX_DEPOSIT_AMOUNT) {
      throw new BusinessException(
        this.i18n.translate('error.MAX_DEPOSIT_AMOUNT', {
          args: { max: TRANSACTION_CONST.MAX_DEPOSIT_AMOUNT },
        }),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    if (
      (user?.accountBalance ?? 0) + amount >
      TRANSACTION_CONST.MAX_ACCOUNT_AMOUNT_BALANCE
    ) {
      throw new BusinessException(
        this.i18n.translate('error.MAX_ACCOUNT_AMOUNT_BALANCE', {
          args: { max: TRANSACTION_CONST.MAX_ACCOUNT_AMOUNT_BALANCE },
        }),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const depositCountKey = `${TRANSACTION_CONST.DEPOSIT_COUNT_KEY_PREFIX}${userId}`;
    const userDepositCount =
      (await this.cacheManager.get<number>(depositCountKey)) || 0;

    if (userDepositCount >= TRANSACTION_CONST.MAX_DEPOSIT_REQUESTS) {
      throw new BusinessException(
        this.i18n.translate('error.DEPOSIT_RATE_LIMIT_EXCEEDED', {
          args: { minutes: TRANSACTION_CONST.DEPOSIT_RATE_LIMIT_MINUTES },
        }),
        ResponseCodeEnum.TOO_MANY_REQUESTS,
      );
    }

    const depositCode = `${generateRandomString(6)}`;

    try {
      let urlPayment = '';

      switch (paymentType) {
        case PAYMENT_TYPE_ENUM.VNPAY:
          urlPayment = this.createVnPayUrl(amount, depositCode);
          break;
        case PAYMENT_TYPE_ENUM.ZALOPAY:
          urlPayment = await this.createZaloPayUrl(amount, depositCode);
          break;
        case PAYMENT_TYPE_ENUM.MOMO:
          urlPayment = await this.createMomoUrl(amount, depositCode);
          break;
        default:
          return new ResponseBuilder()
            .withCode(ResponseCodeEnum.BAD_REQUEST)
            .withMessage('Phương thức thanh toán không hợp lệ')
            .build();
      }

      await this.cacheManager.set(
        `${TRANSACTION_CONST.DEPOSIT_DATA_KEY_PREFIX}${depositCode}`,
        {
          userId,
          amount,
        },
        TRANSACTION_CONST.DEPOSIT_RATE_LIMIT_MINUTES * 60 * 1000,
      );

      await this.cacheManager.set(
        depositCountKey,
        userDepositCount + 1,
        TRANSACTION_CONST.DEPOSIT_RATE_LIMIT_MINUTES * 60 * 1000,
      );

      return new ResponseBuilder({ urlPayment })
        .withCode(ResponseCodeEnum.SUCCESS)
        .withMessage(
          this.i18n.translate('message.CREATE_DEPOSIT_REQUEST_SUCCESS'),
        )
        .build();
    } catch (error) {
      this.logger.error(
        `[CREATE DEPOSIT ERROR]: ${error} - ${JSON.stringify(error)}`,
      );
      return new ResponseBuilder()
        .withCode(ResponseCodeEnum.INTERNAL_SERVER_ERROR)
        .withMessage(this.i18n.translate('error.INTERNAL_SERVER_ERROR'))
        .build();
    }
  }

  private async callbackVnPay(
    request: VnPayReturnRequestDto,
  ): Promise<{ result: boolean; token?: string }> {
    let vnp_Params = request;

    const secretKey = this.vnPayConfig.hashSecret;
    const secureHash = vnp_Params['vnp_SecureHash'];

    delete vnp_Params['vnp_SecureHash'];
    delete vnp_Params['vnp_SecureHashType'];

    vnp_Params = this.sortObject(vnp_Params);

    const hmac = crypto.createHmac('sha512', secretKey);
    const signData = queryString.stringify(vnp_Params, { encode: false });
    const signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');

    if (secureHash === signed) {
      const depositCode = vnp_Params['vnp_TxnRef'];
      return this.processDeposit(depositCode, PAYMENT_TYPE_ENUM.VNPAY);
    } else {
      return { result: false };
    }
  }

  private async callbackZaloPay(
    request: ZaloPayReturnRequestDto,
  ): Promise<{ result: boolean; token?: string }> {
    const requestData = plainToInstance(ZaloPayReturnRequestDto, request, {
      excludeExtraneousValues: true,
    });
    const checksumData = [
      requestData.appId,
      requestData.appTransId,
      requestData.pmcId,
      requestData.bankCode,
      requestData.amount,
      requestData.discountAmount,
      requestData.status,
    ].join('|');

    const checksum = crypto
      .createHmac('sha256', this.zaloPayConfig.key2)
      .update(checksumData)
      .digest('hex');

    if (checksum === requestData.checksum) {
      const depositCode = requestData.appTransId.split('_')[1];
      return this.processDeposit(depositCode, PAYMENT_TYPE_ENUM.ZALOPAY);
    } else {
      return { result: false };
    }
  }

  private async callbackMomo(
    request: MomoReturnRequestDto,
  ): Promise<{ result: boolean; token?: string }> {
    const momoParams = request;
    const depositCode = momoParams['orderId'];
    return this.processDeposit(depositCode, PAYMENT_TYPE_ENUM.MOMO);
  }

  async handleCallbackPayment(
    request:
      | MomoReturnRequestDto
      | ZaloPayReturnRequestDto
      | VnPayReturnRequestDto,
    bank: PAYMENT_ENDPOINT_ENUM,
  ): Promise<{ result: boolean; token?: string }> {
    switch (bank) {
      case PAYMENT_ENDPOINT_ENUM.MOMO:
        return this.callbackMomo(request as MomoReturnRequestDto);
      case PAYMENT_ENDPOINT_ENUM.VNPAY:
        return this.callbackVnPay(request as VnPayReturnRequestDto);
      case PAYMENT_ENDPOINT_ENUM.ZALOPAY:
        return this.callbackZaloPay(request as ZaloPayReturnRequestDto);
      default:
        return { result: false };
    }
  }

  async verify(request: VerifyTransactionRequestDto) {
    const { token } = request;

    const tokenExist = await this.cacheManager.get<string>(token);
    if (!tokenExist) {
      throw new BusinessException(
        this.i18n.translate('error.BAD_REQUEST'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    await this.cacheManager.del(token);
    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  private async processDeposit(
    depositCode: string,
    paymentType: PAYMENT_TYPE_ENUM,
  ): Promise<{ result: boolean; token?: string }> {
    const keyCache = `${TRANSACTION_CONST.DEPOSIT_DATA_KEY_PREFIX}${depositCode}`;
    const depositData = await this.cacheManager.get<DepositData>(keyCache);

    if (!depositData) {
      return { result: false };
    }

    const user = await this.userRepository.findById(depositData.userId);

    if (!user) {
      throw new BusinessException(
        this.i18n.translate('error.USER_NOT_FOUND'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    const session = await this.connection.startSession();
    session.startTransaction();

    try {
      const paymentNote = this.getPaymentNote(depositData.amount, paymentType);

      await this.transactionRepository.create(
        {
          user: depositData.userId,
          code: depositCode,
          amount: depositData.amount,
          balanceBefore: user?.accountBalance,
          balanceAfter: user?.accountBalance + depositData.amount,
          action: TRANSACTION_ACTION_ENUM.DEPOSIT,
          note: paymentNote,
        },
        { session },
      );

      await this.userRepository.updateOne(
        { _id: depositData.userId },
        { $inc: { accountBalance: depositData.amount } },
        { session },
      );

      await session.commitTransaction();

      await this.cacheManager.del(keyCache);

      const depositCountKey = `${TRANSACTION_CONST.DEPOSIT_COUNT_KEY_PREFIX}${depositData.userId}`;
      const currentCount =
        (await this.cacheManager.get<number>(depositCountKey)) || 0;

      if (currentCount > 0) {
        await this.cacheManager.set(depositCountKey, currentCount - 1);
      }

      const token = generateRandomString(20);
      await this.cacheManager.set(
        token,
        true,
        TRANSACTION_CONST.VERIFY_TRANSACTION_TOKEN_EXPIRED_TIME,
      );

      return { result: true, token };
    } catch (error) {
      await session.abortTransaction();
      this.logger.error(
        `[CALLBACK ${paymentType} ERROR]: ${error} - ${JSON.stringify(error)}`,
      );
      return { result: false };
    } finally {
      await session.endSession();
    }
  }

  private async createMomoUrl(
    amountDeposit: number | string,
    code: string,
  ): Promise<string> {
    var accessKey = this.momoConfig.accessKey.toString();
    var secretKey = this.momoConfig.secretKey.toString();
    var orderInfo = `Nap tien vao tai khoan: ${code} qua Momo`;
    var partnerCode = this.momoConfig.partnerCode;
    var redirectUrl = this.momoConfig.redirectUrl;
    var ipnUrl = this.momoConfig.ipnUrl;
    var requestType = 'payWithMethod';
    var amount = amountDeposit.toString();
    var orderId = code;
    var requestId = code;
    var extraData = '';
    var orderGroupId = '';
    var autoCapture = true;
    var lang = 'vi';

    const rawSignature =
      'accessKey=' +
      accessKey +
      '&amount=' +
      amount +
      '&extraData=' +
      extraData +
      '&ipnUrl=' +
      ipnUrl +
      '&orderId=' +
      orderId +
      '&orderInfo=' +
      orderInfo +
      '&partnerCode=' +
      partnerCode +
      '&redirectUrl=' +
      redirectUrl +
      '&requestId=' +
      requestId +
      '&requestType=' +
      requestType;

    const signature = crypto
      .createHmac('sha256', secretKey)
      .update(rawSignature)
      .digest('hex');

    const requestBody = JSON.stringify({
      partnerCode,
      partnerName: this.momoConfig.partnerName,
      storeId: this.momoConfig.storeId,
      requestId,
      amount,
      orderId,
      orderInfo,
      redirectUrl,
      ipnUrl,
      lang,
      requestType,
      autoCapture,
      extraData,
      orderGroupId,
      signature,
    });

    try {
      const options = {
        method: 'POST',
        url: this.momoConfig.apiUrl,
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(requestBody),
        },
        data: requestBody,
      };

      const { data } = await axios(options);

      const response = plainToInstance(PaymentMomoResponseDto, data, {
        excludeExtraneousValues: true,
      });

      return response.payUrl;
    } catch (error) {
      throw new BusinessException(
        'Đã sảy ra lỗi tạo URL thanh toán vui lòng thử lại sau',
        ResponseCodeEnum.BAD_REQUEST,
      );
    }
  }

  private async createZaloPayUrl(
    amountDeposit: number,
    code: string,
  ): Promise<string> {
    const embed_data = {
      orderId: code,
      redirecturl: this.zaloPayConfig.redirectUrl,
    };

    const items = [{}];

    const orderData = {
      app_id: this.zaloPayConfig.appId,
      app_trans_id: `${moment().format('YYMMDD')}_${code}`,
      app_user: this.zaloPayConfig.appUser,
      app_time: Date.now(),
      item: JSON.stringify(items),
      embed_data: JSON.stringify(embed_data),
      amount: amountDeposit,
      description: `Nạp thành công ${formatCurrency(amountDeposit)} VNĐ vào tài khoản`,
      bank_code: '',
      callback_url: this.zaloPayConfig.callbackUrl,
    };

    const data = [
      this.zaloPayConfig.appId,
      orderData.app_trans_id,
      orderData.app_user,
      orderData.amount,
      orderData.app_time,
      orderData.embed_data,
      orderData.item,
    ].join('|');

    orderData['mac'] = crypto
      .createHmac('sha256', this.zaloPayConfig.key1)
      .update(data)
      .digest('hex');

    try {
      const { data } = await axios.post(this.zaloPayConfig.apiUrl, null, {
        params: orderData,
      });

      const response = plainToInstance(PaymentZaloPayResponseDto, data, {
        excludeExtraneousValues: true,
      });

      return response?.orderUrl;
    } catch (error) {
      throw new BusinessException(
        'Đã sảy ra lỗi tạo URL thanh toán vui lòng thử lại sau',
        ResponseCodeEnum.BAD_REQUEST,
      );
    }
  }

  private createVnPayUrl(amount: number, code: string): string {
    const locale = 'vn';
    const currCode = 'VND';
    const createDate = moment().format('YYYYMMDDHHmmss');

    let vnpUrl = this.vnPayConfig.VnPayUrl;
    const tmnCode = this.vnPayConfig.tmnCode;
    const returnUrl = this.vnPayConfig.depositUrl;
    const secretKey = this.vnPayConfig.hashSecret;

    let VnPayParams = {};

    VnPayParams['vnp_TxnRef'] = code;
    VnPayParams['vnp_Command'] = 'pay';
    VnPayParams['vnp_Locale'] = locale;
    VnPayParams['vnp_TmnCode'] = tmnCode;
    VnPayParams['vnp_Version'] = '2.1.0';
    VnPayParams['vnp_CurrCode'] = currCode;
    VnPayParams['vnp_OrderType'] = 'other';
    VnPayParams['vnp_IpAddr'] = '127.0.0.1';
    VnPayParams['vnp_ReturnUrl'] = returnUrl;
    VnPayParams['vnp_CreateDate'] = createDate;
    VnPayParams['vnp_Amount'] = amount * 100;
    VnPayParams['vnp_OrderInfo'] = `Nap tien vao tai khoan: ${code} qua VNPAY`;

    VnPayParams = this.sortObject(VnPayParams);

    const hmac = crypto.createHmac('sha512', secretKey);
    const signData = queryString.stringify(VnPayParams, { encode: false });
    const signed = hmac.update(Buffer.from(signData, 'utf-8')).digest('hex');

    VnPayParams['vnp_SecureHash'] = signed;
    vnpUrl += '?' + queryString.stringify(VnPayParams, { encode: false });

    return vnpUrl;
  }

  private sortObject(obj: any): any {
    const str = [];
    const sorted = {};

    let key;
    for (key in obj) {
      if (obj.hasOwnProperty(key)) {
        str.push(encodeURIComponent(key));
      }
    }

    str.sort();

    for (key = 0; key < str.length; key++) {
      sorted[str[key]] = encodeURIComponent(obj[str[key]]).replace(/%20/g, '+');
    }

    return sorted;
  }

  private getPaymentNote(
    amount: number,
    paymentType: PAYMENT_TYPE_ENUM,
  ): string {
    const formattedAmount = formatCurrency(amount);

    switch (paymentType) {
      case PAYMENT_TYPE_ENUM.MOMO:
        return `Nạp thành công ${formattedAmount} VNĐ vào tài khoản qua Momo`;
      case PAYMENT_TYPE_ENUM.ZALOPAY:
        return `Nạp thành công ${formattedAmount} VNĐ vào tài khoản qua ZaloPay`;
      case PAYMENT_TYPE_ENUM.VNPAY:
        return `Nạp thành công ${formattedAmount} VNĐ vào tài khoản qua VNPay`;
      default:
        return `Nạp thành công ${formattedAmount} VNĐ vào tài khoản`;
    }
  }
}
