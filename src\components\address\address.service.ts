import { isEmpty } from 'lodash';
import { I18nService } from 'nestjs-i18n';
import { plainToInstance } from 'class-transformer';
import { Inject, Injectable } from '@nestjs/common';

import { ResponseBuilder } from '@utils/response-builder';
import { IdParamDto } from '@core/dto/param-id.request.dto';
import { BaseResponseDto } from '@core/dto/base.response.dto';
import { ResponseCodeEnum } from '@constant/response-code.enum';
import { ADDRESS_CONST, IS_DEFAULT_ADDRESS_ENUM } from './address.constant';
import { GetMyAddressRequestDto } from './dto/request/get-my-address.request.dto';
import { CreateAddressRequestDto } from './dto/request/create-address.request.dto';
import { UpdateAddressRequestDto } from './dto/request/update-address.request.dto';
import { BusinessException } from '@core/exception-filter/business-exception.filter';
import { GetDetailAddressResponseDto } from './dto/response/get-address-detail.response.dto';
import { AddressRepositoryInterface } from '@database/repository/address/address.repository.interface';

@Injectable()
export class AddressService {
  constructor(
    private readonly i18n: I18nService,

    @Inject('AddressRepositoryInterface')
    private readonly addressRepository: AddressRepositoryInterface,
  ) {}
  async create(request: CreateAddressRequestDto) {
    const { userId, isDefault } = request;

    const countAddress = await this.addressRepository.countDocuments({
      user: userId,
    });

    if (countAddress >= ADDRESS_CONST.MAXIMUM_QUANTITY) {
      throw new BusinessException(
        this.i18n.translate('error.ADDRESS_MAXIMUM_QUANTITY', {
          args: { max: ADDRESS_CONST.MAXIMUM_QUANTITY },
        }),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    if (isDefault === IS_DEFAULT_ADDRESS_ENUM.YES) {
      await this.resetDefaultAddress(userId);
    }

    const addressEntity = this.addressRepository.createEntity(request);
    await addressEntity.save();

    const response = plainToInstance(BaseResponseDto, addressEntity, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.CREATED)
      .withMessage(this.i18n.translate('message.CREATE_SUCCESS'))
      .build();
  }

  async list(request: GetMyAddressRequestDto) {
    const { data, total } = await this.addressRepository.list(request);

    const response = plainToInstance(GetDetailAddressResponseDto, data, {
      excludeExtraneousValues: true,
    });

    return new ResponseBuilder({
      items: response,
      meta: { total, page: request.page },
    })
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SUCCESS'))
      .build();
  }

  async update(request: UpdateAddressRequestDto) {
    const { id, userId } = request;

    const address = await this.addressRepository.findOne({
      _id: id,
      user: userId,
    });

    if (isEmpty(address)) {
      throw new BusinessException(
        this.i18n.translate('error.NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    if (address.isDefault !== IS_DEFAULT_ADDRESS_ENUM.YES) {
      await this.resetDefaultAddress(userId);
    }

    const addressEntity = this.addressRepository.updateEntity(address, request);
    await addressEntity.save();

    const response = plainToInstance(
      GetDetailAddressResponseDto,
      addressEntity,
      {
        excludeExtraneousValues: true,
      },
    );

    return new ResponseBuilder(response)
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.UPDATE_SUCCESS'))
      .build();
  }

  async delete(request: IdParamDto) {
    const { id, userId } = request;

    const address = await this.addressRepository.findOne({
      _id: id,
      user: userId,
    });

    if (isEmpty(address)) {
      throw new BusinessException(
        this.i18n.translate('error.NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    if (address.isDefault === IS_DEFAULT_ADDRESS_ENUM.YES) {
      throw new BusinessException(
        this.i18n.translate('error.CANNOT_DELETE_DEFAULT_ADDRESS'),
        ResponseCodeEnum.BAD_REQUEST,
      );
    }

    await this.addressRepository.deleteOne({ _id: id });

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.DELETE_SUCCESS'))
      .build();
  }

  async setDefaultAddress(request: IdParamDto) {
    const { id, userId } = request;

    const address = await this.addressRepository.findOne({
      _id: id,
      user: userId,
    });

    if (isEmpty(address)) {
      throw new BusinessException(
        this.i18n.translate('error.NOT_FOUND'),
        ResponseCodeEnum.NOT_FOUND,
      );
    }

    await this.resetDefaultAddress(userId);

    address.isDefault = IS_DEFAULT_ADDRESS_ENUM.YES;
    await address.save();

    return new ResponseBuilder()
      .withCode(ResponseCodeEnum.SUCCESS)
      .withMessage(this.i18n.translate('message.SET_ADDRESS_DEFAULT_SUCCESS'))
      .build();
  }

  private async resetDefaultAddress(userId: string) {
    await this.addressRepository.updateMany(
      { user: userId },
      { isDefault: IS_DEFAULT_ADDRESS_ENUM.NO },
    );
  }
}
