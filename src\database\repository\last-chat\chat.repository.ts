import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

import { LastChat as LastChatModel } from '@database/schemas/last-chat.model';
import { LastChatRepositoryInterface } from './last-chat.repository.interface';
import { BaseAbstractRepository } from '@core/repository/base.abstract.repository';

export class LastChatRepository
  extends BaseAbstractRepository<LastChatModel>
  implements LastChatRepositoryInterface
{
  constructor(
    @InjectModel('LastChat')
    private readonly lastChatModel: Model<LastChatModel>,
  ) {
    super(lastChatModel);
  }
}
