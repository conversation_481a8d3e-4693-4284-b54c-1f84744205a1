{"IS_INSTANCE": "$IS_INSTANCE decorator expects and object as value, but got falsy value.", "IS_DECIMAL": "{property} ph<PERSON>i là số thập phân", "IS_BIC_OR_SWIFT_CODE": "{property} phải là một BIC hoặc SWIFT mã", "IS_BOOLEAN_STRING": "{property} phải là một boolean string", "IS_BOOLEAN": "{property} phải là một boolean value", "IS_BTC_ADDRESS": "{property} phải là một BTC địa chỉ ", "IS_CREDIT_CARD": "{property} phải là một credit card", "IS_CURRENCY": "{property} phải là một currency", "IS_URI_FORMAT": "{property} phải là một data uri format", "IS_DATE": "{property} phải là một Date instance", "IS_FIREBASE_PUSH_ID": "{property} phải là một Firebase Push Id", "IS_HASH_OF_TYPE_": "{property} phải là một hash of type {constraint1}", "IS_HEXADECIMAL_COLOR": "{property} phải là một hexadecimal color", "IS_HEXADECIMAL_NUBMER": "{property} phải là một hexadecimal số ", "IS_HSL_COLOR": "{property} phải là một HSL color", "IS_IDENTITY_CARD_NUMBER": "{property} phải là một identity card số ", "IS_ISSN": "{property} phải là một ISSN", "IS_JSON_STRING": "{property} phải là một json string", "JWT_STRING": "{property} phải là một jwt string", "IS_LATITUDE_STRING_OR_NUMBER": "{property} phải là một latitude string hoặc số ", "IS_LATITUDE_LONGTITUDE_STRING": "{property} phải là một latitude,longitude string", "IS_LINGTITUDE_STRING_OR_NUMBER": "{property} phải là một longitude string hoặc số ", "IS_LOWER_CASE_STRING": "{property} phải là một lowercase string", "IS_MAC_ADDRESS": "{property} phải là một MAC địa chỉ ", "IS_MONGODB_ID": "{property} kh<PERSON><PERSON> hợp lệ", "IS_NAGATIVE_NUMBER": "{property} phải là một negative số ", "IS_A_NON_EMPTY_OBJECT": "{property} phải là một non-empty object", "IS_A_NUMBER_CONFIRMING_TO_THE_SPECIFICIED": "{property} phải là một số conforming to the specified constraints", "IS_A_NUMBER_STRING": "{property} phải là một số string", "IS_A_PHONE_NUMBER": "{property} phải là một phone số ", "IS_A_PORT": "{property} phải là một port", "IS_A_POSITIVE_NUMBER": "{property} ph<PERSON>i là một dương số ", "IS_A_POSTAL_CODE": "{property} phải là một postal mã", "IS_A_SEMANTIC_VERSIONING_SPECIFICATION": "{property} phải là một Semantic Versioning Specification", "IS_A_STRING": "{property} phải là một string", "IS_A_VALID_DOMAIN_NAME": "{property} phải là một tên domain đúng", "PROPERTY_MUST_BE_A_VALID_ENUM_VALUE": "{property} phải là một enum giá trị", "PROPERTY_MUST_BE_A_VALID_ISO_8601_DATE_STRING": "{property} phải là định dạng ISO 8601 ", "PROPERTY_MUST_BE_A_VALID_ISO31661_ALPHA2_CODE": "{property} phải là ISO31661 Alpha2", "PROPERTY_MUST_BE_A_VALID_ISO31661_ALPHA3_CODE": "{property} phải là ISO31661 Alpha3", "PROPERTY_MUST_BE_A_VALID_PHONE_NUMBER": "{property} ph<PERSON>i là một số điện thoại", "PROPERTY_MUST_BE_A_VALID_REPRESENTATION_OF_MILITARY_TIME_IN_THE_FORMAT_HH_MM": "{property} phải là một valid representation of military time in the format HH:MM", "PROPERTY_MUST_BE_AN_ARRAY": "{property} ph<PERSON>i là một mảng", "PROPERTY_MUST_BE_AN_EAN": "{property} phải là an EAN (European Article số )", "PROPERTY_MUST_BE_AN_EMAIL": "{property} ph<PERSON><PERSON> là an email", "PROPERTY_MUST_BE_AN_ETHEREUM_ADDRESS": "{property} phải là an Ethereum địa chỉ ", "PROPERTY_MUST_BE_AN_IBAN": "{property} phải là một IBAN", "PROPERTY_MUST_BE_AN_INSTANCE_OF": "{property} ph<PERSON><PERSON> là an instance of {constraint1}", "PROPERTY_MUST_BE_AN_INTEGER_NUMBER": "{property} phải là một số dương", "PROPERTY_MUST_BE_AN_IP_ADDRESS": "{property} phải là một địa chỉ ip", "PROPERTY_MUST_BE_AN_ISBN": "{property} phải là an ISBN", "PROPERTY_MUST_BE_AN_ISIN": "{property} phải là an ISIN (stock/security identifier)", "PROPERTY_MUST_BE_AN_ISRC": "{property} phải là an ISRC", "PROPERTY_MUST_BE_AN_OBJECT": "{property} phải là một object", "PROPERTY_MUST_BE_AN_ADDRESS": "{property} phải là một địa chỉ URL", "PROPERTY_MUST_BE_AN_URL_UUID": "{property} phải là UUID", "PROPERTY_MUST_BE_BASED32_ENCODED": "{property} phải là base32 encode", "PROPERTY_MUST_BE_BASE64_ENCODED": "{property} phải là base64 encode", "PROPERTY_MUST_BE_DEVISIBLE_BY": "{property} ph<PERSON>i là chia hêt cho {constraint1}", "PROPERTY_MUST_BE_EMPTY": "{property} phải là rỗng", "PROPERTY_MUST_BE_EQUAL_TO": "{property} phải bằng {constraint1}", "PROPERTY_MUST_BE_A_LOCALE": "{property} phải là locale", "PROPERTY_MUST_BE_LONGER_THAN_OR_EQUAL_TO_S_AND_SHORTER_THAN_OR_EQUAL_TO_S_CHARACTER": "{property} ph<PERSON>i là longer than hoặc equal to {constraint1} and shorter than hoặc equal to {constraint1} ký tự", "PROPERTY_MUST_BE_LONGER_THAN_OR_EQUAL_TO_S_CHARACTER": "{property} c<PERSON> <PERSON>t nhất {constraint1} ký tự", "PROPERTY_MUST_BE_MAGNET_URI_FORAMT": "{property} phải là magnet uri format", "PROPERTY_MUST_BE_MIME_TYPE_FORMAT": "{property} phải là MIME type format", "PROPERTY_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE_D": "{property} ph<PERSON><PERSON> là one of the following values: {constraint1}", "PROPERTY_MUST_BE_RFC_339_DATE": "{property} phải là RFC 3339", "PROPERTY_MUST_BE_RGB_COLOR": "{property} phải là RGB", "PROPERTY_MUST_BE_SHOTER_THAN_OR_EQUAL_S_CHARACTERS": "{property} phải nhỏ hơn hoặc bằng {constraint1} ký tự", "PROPERTY_MUST_BE_UPPERCASE": "{property} ph<PERSON>i là viết hoa", "PROPERTY_MUST_BE_VALID_OCTAL_NUMBER": "{property} phải là valid octal số ", "PROPERTY_MUST_CONTAIN_PASSPORT_NUMBER": "{property} phải là valid passport số ", "PROPERTY_MUST_CONTAIN_A_S_VALUE": "{property} must contain {constraint1} values", "PROPERTY_MUST_CONTAIN_A_S_STRING": "{property} must contain một {constraint1} string", "PROPERTY_MUST_CONTAIN_A_FULL_WIDTH_AND_HALF_WIDTH_CHARACTER": "{property} must contain một full-width and half-width ký tự", "PROPERTY_MUST_CONTAIN__FULL_WIDTH_CHARACTER": "{property} must contain một full-width ký tự", "PROPERTY_MUST_CONTAIN_A_HALF_WIDTH_CHARACTER": "{property} must contain một half-width ký tự", "PROPERTY_MUST_CONTAIN_ANY_SURROGATE_PAIRS_CHARS": "{property} must contain any surrogate pairs chars", "PROPERTY_MUST_CONTAIN_AT_LEAST_ELEMENTS": "{property} must contain at least {constraint1} elements", "PROPERTY_MUST_CONTAIN_NOT_MORE_THAN_ELEMENTS": "{property} must contain not more than {constraint1} elements", "PROPERTY_MUST_CONTAIN_ONE_OR_MORE_MULTIBYTE_CHARS": "{property} must contain one hoặc more multibyte chars", "PROPERTY_MUST_CONTAIN_ONLY_ASCII_CHARACTERS": "{property} must contain only ASCII ký tự", "PROPERTY_MUST_CONTAIN_ONLY_LETTERS_AZAZ": "{property} chỉ chứa các ký tự [a-zA-Z]", "PROPERTY_MUST_CONTAIN_ONLY_LETTERS_AND_NUMBER": "{property} chỉ chứa chữ và số", "PROPERTY_MUST_MATCH_REGULAR_EXPRESSION": "{property} kh<PERSON><PERSON> hợp lệ", "PROPERTY_SHOULD_NOT_BE_GREATER_THAN": "{property} ph<PERSON>i nhỏ hơn {constraint1}", "PROPERTY_SHOULD_NOT_BE_LESS_THAN": "{property} kh<PERSON><PERSON> đ<PERSON> nhỏ hơn {constraint1}", "PROPERTY_SHOULD_NOT_BE_EMPTY": "{property} kh<PERSON>ng đ<PERSON> rỗng", "PROPERTY_SHOULD_NOT_BE_EQUAL_TO": "{property} ph<PERSON>i kh<PERSON>c {constraint1}", "PROPERTY_SHOULD_NOT_BE_NULL_OR_UNDEFINED": "{property} ph<PERSON>i không tồn tại", "PROPERTY_SHOULD_NOT_BE_ONE_OF_THE_FOLLOWING_VALUES": "{property} should not be one of the following values: {constraint1}", "PROPERTY_SHOLD_NOT_CONATAIN_VALUE": "{property} should not contain {constraint1} values", "PROPERTY_SHOLD_NOT_CONATAIN_A_STRING": "{property} should not contain một {constraint1} string", "PROPERTY_BYTE_LENGTH_MUST_FALL_INTO": "{property}'s byte length must fall into \\({constraint1}, {constraint1}\\) range", "ALL_PROPERTY_ELEMENTS_MUST_BE_UNIQUE": "<PERSON><PERSON><PERSON> cả các phần tử của {property} ph<PERSON>i khác nhau", "EACH_VALUE_IN": "Mỗi giá trị trong ", "MAXIMAL_ALLOWED_DATE_FOR": "maximal allowed date for {property} is {constraint1}", "MINIMAL_ALLOWED_DATE_FOR": "minimal allowed date for {property} is {constraint1}", "NESTED_PROPERTY_MUST_BE_EITHER_OBJECT_OR_ARRAY": "nested property {property} phải là either object hoặc array", "PROPERTY_MUST_BE_NOSQL_ID": "{property} phải là một NoSQL ID hợp lệ", "PROPERTY_MUST_BE_SQL_ID": "{property} phải là một SQL ID hợp lệ", "PROPERTY_MUST_BE_SQL_OR_NOSQL_ID": "{property} phải là một SQL hoặc NoSQL ID hợp lệ", "PROPERTY_IS_REQUIRED": "Trường {property} là trường bắt buộc", "PROPERTY_VALUE_MAX_LENGTH": "<PERSON><PERSON><PERSON> trị trường {property} không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {maxLength} kí tự", "PROPERTY_IS_NOT_BLANK": "{property} kh<PERSON><PERSON> hợp lệ", "NOT_INCLUDE_SPECIAL_CHARACTER": "{property} kh<PERSON><PERSON> đư<PERSON><PERSON> chứa ký tự đặc biệt", "MSG_194_1": "<PERSON><PERSON><PERSON> kho không tồn tại trong hệ thống"}