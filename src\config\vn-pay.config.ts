import { IsString } from 'class-validator';
import { registerAs } from '@nestjs/config';

import { VnPayConfig } from './config.type';
import validateConfig from '@utils/validate-config';

class EnvironmentVariablesValidator {
  @IsString()
  VN_PAY_URL: string;

  @IsString()
  VN_PAY_TMN_CODE: string;

  @IsString()
  VN_PAY_RETURN_URL: string;

  @IsString()
  VN_PAY_DEPOSIT_URL: string;

  @IsString()
  VN_PAY_SECURE_SECRET: string;
}

export default registerAs<VnPayConfig>('vnPay', () => {
  validateConfig(process.env, EnvironmentVariablesValidator);

  return {
    VnPayUrl: process.env.VN_PAY_URL,
    tmnCode: process.env.VN_PAY_TMN_CODE,
    returnUrl: process.env.VN_PAY_RETURN_URL,
    depositUrl: process.env.VN_PAY_DEPOSIT_URL,
    hashSecret: process.env.VN_PAY_SECURE_SECRET,
  };
});
