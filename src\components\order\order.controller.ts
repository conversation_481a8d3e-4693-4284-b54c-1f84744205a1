import {
  Put,
  Res,
  Get,
  Body,
  Post,
  Param,
  Query,
  Delete,
  Request,
  UseGuards,
  Controller,
} from '@nestjs/common';
import { isEmpty } from 'lodash';
import { Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { mergePayload } from '@utils/common';
import { OrderService } from './order.service';
import { RoleGuard } from '@core/guards/role.guard';
import { AllConfigType } from '@config/config.type';
import { BaseDto } from '@core/dto/base.request.dto';
import { Roles } from '@core/decorators/roles.decorator';
import { Public } from '@core/decorators/public.decorator';
import { IdParamDto } from '@core/dto/param-id.request.dto';
import { USER_ROLE_ENUM } from '@components/user/user.constant';
import { CreateOrderRequestDto } from './dto/request/create-order.request.dto';
import { CancelOrderRequestDto } from './dto/request/cancel-order.request.dto';
import { GetListOrderRequestDto } from './dto/request/get-list-order.request.dto';
import { ShippingOrderRequestDto } from './dto/request/shipping-order.request.dto';
import { VnPayReturnRequestDto } from '@components/order/dto/request/vnpay-return.request.dto';
import { RejectOrderRequestDto } from '@components/order/dto/request/reject-order.request.dto';

@ApiBearerAuth()
@Controller('orders')
export class OrderController {
  constructor(
    private readonly orderService: OrderService,

    private readonly configService: ConfigService<AllConfigType>,
  ) {}

  @Public()
  @Get('/callback/vnpay')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Callback thành toán VnPay',
    description: 'Callback thành toán VnPay',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async callbackVnPay(
    @Query() query: VnPayReturnRequestDto,
    @Res() res: Response,
  ) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    const { result, token } = await this.orderService.callbackVnPay(request);

    const appConfig = this.configService.getOrThrow('app', { infer: true });
    const frontendUrl = appConfig.frontendDomain || 'http://localhost:3000';

    if (!result) {
      res.redirect(frontendUrl);
    }

    return res.redirect(`${frontendUrl}/payment/verify?token=${token}`);
  }

  @Post('')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Tạo đơn hàng',
    description: 'Tạo đơn hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async create(@Body() payload: CreateOrderRequestDto) {
    const { request, responseError } = payload;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.orderService.create(request);
  }

  @Get('/me')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Danh sách đơn hàng dành cho người dùng',
    description: 'Danh sách đơn hàng dành cho người dùng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async listForUser(
    @Request() req: BaseDto,
    @Query() query: GetListOrderRequestDto,
  ) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.orderService.listForUser({
      ...request,
      user: req?.user,
      userId: req?.userId,
    });
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.SHIPPER)
  @Get('/shipper')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Danh sách đơn hàng dành cho shipper',
    description: 'Danh sách đơn hàng dành cho shipper',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async listForShipper(
    @Request() req: BaseDto,
    @Query() query: GetListOrderRequestDto,
  ) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.orderService.listForShipper({
      ...request,
      user: req?.user,
      userId: req?.userId,
    });
  }

  @Get('/statistic-by-status')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Thống kê số lượng đơn hàng theo trạng thái',
    description: 'Thống kê số lượng đơn hàng theo trạng thái',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async statisticByStatus(@Request() request: BaseDto) {
    return await this.orderService.statisticByStatus(request);
  }

  @Get('/:id')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Chi tiết đơn hàng',
    description: 'Chi tiết đơn hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async detail(@Param() param: IdParamDto, @Request() req: BaseDto) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.orderService.detail({
      ...request,
      user: req?.user,
      userId: req?.userId,
    });
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN, USER_ROLE_ENUM.VIEWER)
  @Get('')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Danh sách đơn hàng dành cho quản trị viên',
    description: 'Danh sách đơn hàng dành cho quản trị viên',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async listForAdmin(@Query() query: GetListOrderRequestDto) {
    const { request, responseError } = query;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.orderService.listForAdmin(request);
  }

  @Put('/:id/cancel')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Huỷ đơn hàng',
    description: 'Huỷ đơn hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async cancel(
    @Param() param: IdParamDto,
    @Body() payload: CancelOrderRequestDto,
  ) {
    const { request, responseError } = mergePayload(payload, param);

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.orderService.cancel({ id: param.id, ...request });
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN, USER_ROLE_ENUM.VIEWER)
  @Put('/:id/reject')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Từ chối đơn hàng',
    description: 'Từ chối đơn hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async reject(
    @Param() param: IdParamDto,
    @Body() payload: RejectOrderRequestDto,
  ) {
    const { request, responseError } = mergePayload(payload, param);

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.orderService.reject({ id: param.id, ...request });
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN, USER_ROLE_ENUM.VIEWER)
  @Put('/:id/confirmed')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Xác nhận đơn hàng',
    description: 'Xác nhận đơn hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async confirm(@Param() param: IdParamDto) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.orderService.confirm(request);
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.ADMIN, USER_ROLE_ENUM.VIEWER)
  @Put('/:id/shipping')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Xác nhận đơn hàng',
    description: 'Xác nhận đơn hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async shipping(
    @Param() param: IdParamDto,
    @Body() payload: ShippingOrderRequestDto,
  ) {
    const { request, responseError } = mergePayload(payload, param);

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.orderService.shipping({ id: param.id, ...request });
  }

  @UseGuards(RoleGuard)
  @Roles(USER_ROLE_ENUM.SHIPPER, USER_ROLE_ENUM.ADMIN, USER_ROLE_ENUM.VIEWER)
  @Put('/:id/delivered')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Giao hàng thành công',
    description: 'Giao hàng thành công',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async delivered(@Param() param: IdParamDto) {
    const { request, responseError } = param;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return await this.orderService.delivered(request);
  }

  @Delete('/reset')
  @ApiOperation({
    tags: ['Order'],
    summary: 'Xoá toàn bộ đơn hàng',
    description: 'Xoá toàn bộ đơn hàng',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async reset(@Request() request: BaseDto) {
    return await this.orderService.reset(request);
  }
}
