import { isEmpty } from 'lodash';
import * as moment from 'moment';
import { Alignment, Borders, Font, Workbook } from 'exceljs';

import {
  ROW,
  SHEET,
  EXCEL_STYLE,
  DATE_FORMAT_VN,
  DATE_FORMAT_EXPORT,
} from '@constant/export.constant';

export const formatDateExport = (
  ...dates: (Date | null | undefined)[]
): string => {
  const validDates = dates.filter((date) => date != null);

  if (isEmpty(validDates)) return '';

  if (validDates.length === 1) {
    return `${moment(validDates[0]).format(DATE_FORMAT_EXPORT)}`;
  }

  const formattedDates = validDates.map((date) =>
    moment(date).format(DATE_FORMAT_EXPORT),
  );

  return formattedDates.join(' - ');
};

export const formatStringExport = (
  ...values: (string | null | undefined)[]
): string => {
  const sanitizedValues = values.map((val) => val ?? '');
  const nonEmptyValues = sanitizedValues.filter((val) => val !== '');

  if (isEmpty(nonEmptyValues)) return '';

  return nonEmptyValues.join(' - ');
};

export const formatDateVN = (originalDate: string | Date): string => {
  const convertedDate = moment(originalDate).format(DATE_FORMAT_VN);
  return convertedDate;
};

/**
 * Convert a column number to its corresponding Excel column letter.
 * @param col 1-indexed column number
 * @returns Excel column letter, e.g. 1 -> 'A', 26 -> 'Z', 27 -> 'AA'
 */
export function getColumnLetter(col: number): string {
  let letter = '';
  while (col > 0) {
    const remainder = (col - 1) % 26;
    letter = String.fromCharCode(65 + remainder) + letter;
    col = Math.floor((col - 1) / 26);
  }
  return letter;
}

export function formatNumber(num: number, decimalPlaces: number = 3): string {
  return num.toFixed(decimalPlaces).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

export interface HeaderExport {
  key: string;
  title: string;
  widthInExcel?: number;
}

export interface ExportSheetConfig {
  data: { [key: string]: any }[];
  title: string;
  headers: HeaderExport[];
  sheetName?: string;
}

export function createExportHeaders(
  headerMap: Record<string, string>,
): HeaderExport[] {
  return Object.keys(headerMap).map((key) => ({
    key,
    title: headerMap[key],
    width: 20,
    style: {
      alignment: EXCEL_STYLE.ALIGN_LEFT_MIDDLE,
    },
  }));
}

/**
 * Create a worksheet with a given configuration
 * @param workbook The Excel workbook
 * @param sheetConfig Configuration for the sheet
 * @param sheetIndex Index of the sheet for naming
 * @returns The created workbook
 */
function createWorksheetWithData(
  workbook: Workbook,
  sheetConfig: ExportSheetConfig,
  sheetIndex: number,
): Workbook {
  const { data, title, headers, sheetName } = sheetConfig;

  let countSheet = SHEET.START_SHEET;
  let sheet = sheetName ?? `${SHEET.NAME} ${sheetIndex + 1}`;
  let countRowData = ROW.COUNT_START_ROW;

  data?.forEach((element) => {
    // Determine sheet name, split if data exceeds limit
    let sheetNameFile = sheet;
    if (data?.length > ROW.LIMIT_EXPORT_ON_SHEET) {
      sheetNameFile = `${sheet} ${countSheet}`;
    }

    // Get or create worksheet
    let worksheet = workbook.getWorksheet(sheetNameFile);
    if (countRowData === ROW.COUNT_START_ROW) {
      worksheet = workbook.addWorksheet(sheetNameFile);

      // Merge cells and set title
      worksheet.mergeCells(`A1:${getColumnLetter(headers.length)}2`);
      const titleCell = worksheet.getCell('A1');
      titleCell.value = title;
      titleCell.font = EXCEL_STYLE.TITLE_FONT;
      titleCell.border = <Partial<Borders>>EXCEL_STYLE.BORDER_ALL;
      titleCell.alignment = <Partial<Alignment>>EXCEL_STYLE.ALIGN_CENTER;

      // Set up header row
      const headerRow = worksheet.getRow(3);
      headerRow.values = headers.map((header) => header.title);
      headerRow.eachCell((cell) => {
        cell.font = <Font>EXCEL_STYLE.TITLE_FONT;
        cell.border = <Partial<Borders>>EXCEL_STYLE.BORDER_ALL;
        cell.alignment = <Partial<Alignment>>EXCEL_STYLE.ALIGN_CENTER;
      });
    }

    // Configure columns
    worksheet.columns = headers;
    headers.forEach((item, index) => {
      if (item?.widthInExcel != null) {
        worksheet.columns[index].width = item.widthInExcel;
      }
    });

    // Add row data with border for all cells
    const row = worksheet.addRow({ ...element });
    for (let i = 1; i <= headers.length; i++) {
      const cell = row.getCell(i);
      cell.font = <Font>EXCEL_STYLE.DEFAULT_FONT;
      cell.border = <Partial<Borders>>EXCEL_STYLE.BORDER_ALL;

      // Ensure empty cells are also styled
      if (!cell.value) {
        cell.value = ''; // Explicitly set empty string
      }
    }

    // Manage row and sheet counting
    countRowData++;
    if (countRowData === ROW.COUNT_END_ROW) {
      countSheet++;
      countRowData = ROW.COUNT_START_ROW;
      sheet = sheetName ?? `${SHEET.NAME} ${sheetIndex + 1}`;
    }
  });

  return workbook;
}

/**
 * Export a single sheet to Excel
 * @param data Data to export
 * @param title Sheet title
 * @param headers Column headers
 * @param sheetName Optional sheet name
 * @returns Excel workbook
 */
export function exportOneSheetUtil(
  data: { [key: string]: any }[],
  title: string,
  headers: HeaderExport[],
  sheetName?: string,
): Workbook {
  const workbook = new Workbook();

  return createWorksheetWithData(
    workbook,
    {
      data,
      title,
      headers,
      sheetName,
    },
    1,
  );
}

/**
 * Export multiple sheets to a single Excel workbook
 * @param exportData Array of sheet configurations
 * @returns Excel workbook
 */
export function exportMultiSheet(exportData: ExportSheetConfig[]): Workbook {
  const workbook = new Workbook();

  exportData.forEach((sheetConfig, index) => {
    createWorksheetWithData(workbook, sheetConfig, index);
  });

  return workbook;
}
