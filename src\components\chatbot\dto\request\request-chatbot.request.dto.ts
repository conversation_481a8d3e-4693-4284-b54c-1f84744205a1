import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength } from 'class-validator';

import { BaseDto } from '@core/dto/base.request.dto';
import { CHATBOT_CONST } from '@components/chatbot/chatbot.constant';

export class RequestChatbotRequestDto extends BaseDto {
  @ApiProperty()
  @MaxLength(CHATBOT_CONST.MAX_LENGTH_CONTENT)
  @IsString()
  @IsNotEmpty()
  context: string;
}
