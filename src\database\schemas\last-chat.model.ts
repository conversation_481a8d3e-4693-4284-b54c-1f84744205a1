import * as mongoose from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

import { BaseModel } from '@core/schema/base.model';

@Schema({
  timestamps: true,
  collection: 'last-chats',
  collation: { locale: 'vi' },
})
export class LastChat extends BaseModel {
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  user: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Chat',
    required: true,
  })
  chat: string;
}

export const LastChatSchema = SchemaFactory.createForClass(LastChat);

export type LastChatDocument = LastChat & mongoose.Document;
