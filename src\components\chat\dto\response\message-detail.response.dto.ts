import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

import { BaseResponseDto } from '@core/dto/base.response.dto';

export class MessageDetailResponseDto extends BaseResponseDto {
  @ApiProperty()
  @Expose()
  message: string;

  @ApiProperty()
  @Expose()
  isSeen: number;

  @ApiProperty()
  @Expose()
  isUserMsg: number;

  @ApiProperty()
  @Expose()
  isRevoked: number;

  @ApiProperty()
  @Expose()
  resource: number;
}
