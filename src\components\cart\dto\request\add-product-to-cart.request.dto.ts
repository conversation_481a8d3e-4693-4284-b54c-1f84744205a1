import {
  <PERSON>,
  <PERSON>,
  <PERSON>I<PERSON>,
  <PERSON>S<PERSON>,
  IsMongoId,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

import { BaseDto } from '@core/dto/base.request.dto';
import { CART_CONST } from '@components/cart/cart.constant';

export class AddProductToCartRequestDto extends BaseDto {
  @ApiProperty()
  @IsString()
  @IsMongoId()
  @IsNotEmpty()
  productId: string;

  @ApiProperty()
  @Min(CART_CONST.MIN_QUANTITY)
  @Max(CART_CONST.MAX_QUANTITY)
  @IsInt()
  @IsNotEmpty()
  quantity: number;
}
