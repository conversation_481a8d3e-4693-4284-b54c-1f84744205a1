import { isEmpty } from 'lodash';
import { Post, Controller, Body } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';

import { Public } from '@core/decorators/public.decorator';
import { CaptchaService } from '@components/captcha/captcha.service';
import { VerifyCaptchaRequestDto } from '@components/captcha/dto/request/verify-captcha.request.dto';

@ApiBearerAuth()
@Controller('captcha')
export class CaptchaController {
  constructor(private readonly captchaService: CaptchaService) {}

  @Public()
  @Post('/generate')
  @ApiOperation({
    tags: ['Captcha'],
    summary: 'Generate captcha mới',
    description: 'Generate captcha mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async generate() {
    return this.captchaService.buildCaptchaResponse();
  }

  @Public()
  @Post('/verify')
  @ApiOperation({
    tags: ['Captcha'],
    summary: 'Verify captcha mới',
    description: 'Verify captcha mới',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async verifyCaptcha(@Body() body: VerifyCaptchaRequestDto) {
    const { request, responseError } = body;

    if (!isEmpty(responseError)) {
      return responseError;
    }

    return this.captchaService.verifyCaptcha(request);
  }
}
