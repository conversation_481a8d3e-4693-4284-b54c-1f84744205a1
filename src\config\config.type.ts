export type AppConfig = {
  nodeEnv: string;
  name: string;
  workingDirectory: string;
  frontendDomain?: string;
  backendDomain: string;
  port: number;
  apiPrefix: string;
  fallbackLanguage: string;
  headerLanguage: string;
  ttl: number;
  urlInvite?: string;
  fontsDirectory?: string;
  cloudProvider: string;
  domainFile?: string;
  vnPayTMNCode?: string;
  vnPaySecureSecret?: string;
  telegramChatId?: string;
  telegramLogsBotToken?: string;
  telegramBackupBotToken?: string;
  telegramBackupChatId?: string;
  backupEncryptionKey?: string;
  geminiApiKey: string;
  openaiApiKey: string;
  socketUrl: string;
};

export type AppleConfig = {
  appAudience: string[];
};

export type AuthConfig = {
  accessSecret?: string;
  accessExpires?: string;
  refreshSecret?: string;
  refreshExpires?: string;
  two2FASecret?: string;
  two2FAExpires?: string;
  captchaSecret?: string;
  captchaExpires?: number;
};

export type CloudflareConfig = {
  turnstileSecretKey?: string;
};

export type DatabaseConfig = {
  url?: string;
  type?: string;
  host?: string;
  port?: number;
  password?: string;
  name?: string;
  username?: string;
  logging?: boolean;
  synchronize?: boolean;
  maxConnections: number;
  rejectUnauthorized?: boolean;
};

export type VnPayConfig = {
  VnPayUrl: string;
  returnUrl: string;
  tmnCode: string;
  hashSecret: string;
  depositUrl: string;
};

export type MomoConfig = {
  accessKey: string;
  apiUrl: string;
  ipnUrl: string;
  partnerCode: string;
  partnerName: string;
  redirectUrl: string;
  secretKey: string;
  storeId: string;
};

export type ZaloPayConfig = {
  apiUrl: string;
  appId: string;
  appUser: string;
  callbackUrl: string;
  key1: string;
  key2: string;
  redirectUrl: string;
};

export type AllConfigType = {
  app: AppConfig;
  apple: AppleConfig;
  auth: AuthConfig;
  database: DatabaseConfig;
  cloudflare: CloudflareConfig;
  vnPay: VnPayConfig;
  momo: MomoConfig;
  zaloPay: ZaloPayConfig;
};
